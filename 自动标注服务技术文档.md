# 自动标注服务技术文档

## 📋 概述

自动标注服务是一个基于安全代理模式的文本预处理系统，专为TTS（文本转语音）应用设计。该服务通过后端代理的方式安全地调用外部AI标注API，为用户提供智能的文本标点和语调标记功能。

## 🏗️ 系统架构

### 架构设计图

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端UI    │───▶│   后端代理API   │───▶│  外部标注服务   │
│  (React)    │    │   (Express)     │    │ (geminitts.ai)  │
└─────────────┘    └─────────────────┘    └─────────────────┘
                            │
                            ▼
                   ┌─────────────────┐
                   │   审计日志系统   │
                   │  (文件存储)     │
                   └─────────────────┘
```

### 核心组件

1. **前端服务层** (`AutoTagService`)
2. **后端代理API** (`/api/auto-tag/*`)
3. **安全验证层** (认证 + 权限 + 频率限制)
4. **外部API调用层** (geminitts.aispeak.top)
5. **审计日志系统** (`AutoTagAudit`)

## 🔐 安全架构

### 安全特性

| 安全层级 | 实现方式 | 说明 |
|---------|---------|------|
| **密钥安全** | 后端环境变量存储 | API密钥完全隐藏，前端无法访问 |
| **用户认证** | JWT Token验证 | 必须提供有效的访问令牌 |
| **权限控制** | VIP状态检查 | 需要STANDARD级别会员权限 |
| **频率限制** | 内存计数器 | 每分钟最多10次请求 |
| **输入验证** | 长度和格式检查 | 最大5000字符，格式验证 |
| **超时保护** | AbortController | 30秒请求超时控制 |

### 权限验证流程

```mermaid
flowchart TD
    A[用户请求] --> B{JWT Token验证}
    B -->|失败| C[401 未授权]
    B -->|成功| D{VIP状态检查}
    D -->|非VIP| E[403 需要会员权限]
    D -->|已过期| F[403 会员已过期]
    D -->|有效| G{频率限制检查}
    G -->|超限| H[429 请求过于频繁]
    G -->|通过| I{输入验证}
    I -->|失败| J[400 输入错误]
    I -->|通过| K[调用外部API]
```

## 📡 API接口规范

### 1. 文本处理接口

**端点**: `POST /api/auto-tag/process`

**请求头**:
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

**请求体**:
```json
{
  "text": "这是需要处理的文本内容",
  "language": "auto"  // 可选，默认为"auto"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "processedText": "这是需要处理的文本内容。",
  "originalLength": 12,
  "processedLength": 13,
  "rateLimit": {
    "remaining": 9,
    "resetTime": 1640995200000
  }
}
```

**错误响应**:

| 状态码 | 错误代码 | 说明 |
|-------|---------|------|
| 400 | `INVALID_INPUT` | 请提供要处理的文本内容 |
| 400 | `TEXT_TOO_LONG` | 文本长度不能超过5000字符 |
| 401 | `TOKEN_REQUIRED` | 需要提供访问令牌 |
| 403 | `VIP_REQUIRED` | 自动标注功能需要会员权限 |
| 403 | `VIP_EXPIRED` | 会员已过期，请续费后使用 |
| 429 | `RATE_LIMIT_EXCEEDED` | 请求过于频繁，请稍后重试 |
| 500 | `SERVICE_UNAVAILABLE` | 服务暂时不可用，请稍后重试 |
| 504 | `REQUEST_TIMEOUT` | 请求超时，请稍后重试 |

### 2. 使用状态查询接口

**端点**: `GET /api/auto-tag/status`

**请求头**:
```http
Authorization: Bearer <access_token>
```

**成功响应** (200):
```json
{
  "rateLimit": {
    "maxRequests": 10,
    "remaining": 7,
    "resetTime": 1640995200000,
    "windowMinutes": 1
  },
  "usage": {
    "totalRequests": 25,
    "successfulRequests": 23,
    "failedRequests": 2,
    "totalTextLength": 12500,
    "lastUsed": "2024-01-01",
    "dailyBreakdown": [
      {
        "date": "2024-01-01",
        "requests": 15,
        "textLength": 7500
      }
    ]
  }
}
```

### 3. 管理员统计接口

**端点**: `GET /api/auto-tag/admin/stats`

**权限**: 仅管理员用户可访问

**查询参数**:
- `date`: 可选，指定日期 (YYYY-MM-DD格式)

**成功响应** (200):
```json
{
  "date": "2024-01-01",
  "stats": {
    "totalRequests": 150,
    "successfulRequests": 145,
    "failedRequests": 5,
    "uniqueUsers": 25,
    "totalTextLength": 75000,
    "averageProcessingTime": 1250
  }
}
```

## ⚙️ 配置说明

### 环境变量配置

```bash
# ========== 自动标注服务配置 ==========
# 外部标注API地址
AUTO_TAG_API_URL=https://geminitts.aispeak.top/api/tts/process

# 外部API访问令牌（安全存储，不暴露给前端）
AUTO_TAG_TOKEN=CM8l3Wqf7TaWah7ruIAxAmMZYcAd274MAeAnFkhvxPg0TMPs

# 请求超时时间（毫秒）
AUTO_TAG_TIMEOUT=30000

# 频率限制（每分钟最大请求数）
AUTO_TAG_RATE_LIMIT=10

# 审计日志存储目录（可选）
AUDIT_LOG_DIR=./logs/auto-tag

# 管理员用户列表（逗号分隔）
ADMIN_USERS=admin1,admin2
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|-----|------|-------|------|
| `AUTO_TAG_API_URL` | String | 必填 | 外部标注服务的API地址 |
| `AUTO_TAG_TOKEN` | String | 必填 | 访问外部服务的认证令牌 |
| `AUTO_TAG_TIMEOUT` | Number | 30000 | 请求超时时间（毫秒） |
| `AUTO_TAG_RATE_LIMIT` | Number | 10 | 每分钟最大请求数 |
| `AUDIT_LOG_DIR` | String | ./logs/auto-tag | 审计日志存储目录 |

## 📊 审计日志系统

### 日志记录内容

每次API调用都会记录以下信息：

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "username": "user123",
  "textLength": 150,
  "processedLength": 155,
  "success": true,
  "error": null,
  "processingTime": 1250,
  "userAgent": "Mozilla/5.0...",
  "ip": "*************",
  "rateLimit": {
    "remaining": 9,
    "resetTime": 1640995200000
  }
}
```

### 日志文件组织

```
logs/auto-tag/
├── auto-tag-2024-01-01.log
├── auto-tag-2024-01-02.log
└── auto-tag-2024-01-03.log
```

### 统计功能

- **用户级统计**: 7天内的使用情况
- **系统级统计**: 全局使用情况和性能指标
- **自动清理**: 保留30天的历史日志

## 🔄 工作流程

### 完整请求流程

1. **前端发起请求**
   - 用户在UI中点击"自动标注"按钮
   - 前端调用 `autoTagService.processText()`

2. **后端接收和验证**
   - 验证JWT token有效性
   - 检查用户VIP状态和权限
   - 执行频率限制检查
   - 验证输入文本格式和长度

3. **外部API调用**
   - 使用环境变量中的密钥调用外部服务
   - 设置30秒超时保护
   - 验证返回数据格式

4. **结果处理和记录**
   - 记录详细的审计日志
   - 返回处理结果给前端
   - 更新用户使用统计

5. **前端结果处理**
   - 将处理后的文本更新到输入框
   - 显示剩余使用次数
   - 处理错误情况

### 错误处理机制

```mermaid
flowchart TD
    A[API调用] --> B{调用成功?}
    B -->|是| C[记录成功日志]
    B -->|否| D{错误类型判断}
    D --> E[超时错误 - 504]
    D --> F[权限错误 - 403]
    D --> G[频率限制 - 429]
    D --> H[输入错误 - 400]
    D --> I[服务错误 - 500]
    E --> J[记录错误日志]
    F --> J
    G --> J
    H --> J
    I --> J
    C --> K[返回结果]
    J --> K
```

## 🚀 部署和维护

### 部署要求

1. **环境依赖**
   - Node.js 16+
   - Express.js 4+
   - 文件系统写入权限（用于日志）

2. **配置检查**
   - 确保所有必需的环境变量已设置
   - 验证外部API的连通性
   - 检查日志目录的写入权限

3. **监控建议**
   - 监控API响应时间
   - 跟踪错误率和成功率
   - 定期检查日志文件大小

### 维护任务

- **日志清理**: 系统自动清理30天前的日志
- **性能监控**: 定期检查处理时间和成功率
- **配额管理**: 根据使用情况调整频率限制
- **安全审计**: 定期检查访问日志和异常行为

## 📝 使用示例

### 前端集成示例

```typescript
import { autoTagService } from '@/lib/auth-service'

// 处理文本标注
async function handleAutoTag() {
  try {
    const result = await autoTagService.processText(
      "这是一个测试文本", 
      "auto"
    );
    
    console.log('处理结果:', result.processedText);
    console.log('剩余次数:', result.rateLimit?.remaining);
  } catch (error) {
    console.error('标注失败:', error.message);
  }
}

// 查询使用状态
async function checkUsageStatus() {
  try {
    const status = await autoTagService.getStatus();
    console.log('使用状态:', status);
  } catch (error) {
    console.error('查询失败:', error.message);
  }
}
```

### 后端测试示例

```javascript
// 测试自动标注功能
const testAutoTag = async () => {
  const response = await fetch('http://localhost:3001/api/auto-tag/process', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer your_jwt_token',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      text: '这是一个测试文本',
      language: 'auto'
    })
  });
  
  const result = await response.json();
  console.log('标注结果:', result);
};
```

## 🔍 故障排除

### 常见问题

1. **配置错误**
   - 检查环境变量是否正确设置
   - 验证外部API地址和令牌

2. **权限问题**
   - 确认用户具有有效的VIP权限
   - 检查JWT token是否过期

3. **频率限制**
   - 用户请求过于频繁
   - 建议增加前端防抖处理

4. **网络问题**
   - 外部API服务不可用
   - 网络连接超时

### 调试建议

- 查看审计日志了解详细错误信息
- 使用管理员接口查看系统统计
- 检查服务器日志中的错误信息
- 验证外部API服务的可用性

---

**文档版本**: v1.0  
**最后更新**: 2024-01-01  
**维护者**: 开发团队
