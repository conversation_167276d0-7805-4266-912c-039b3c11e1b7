"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs"
import { ArrowLeft, CreditCard, Clock, Zap, Star, Crown, Sparkles, Diamond, Award, Users } from "lucide-react"
import { auth, cardService } from "@/lib/auth-service"
import { useToast } from "@/hooks/use-toast"
import { getErrorUIState } from "@/lib/error-utils"
import { UserQuotaResponse } from "@/lib/api"

// 套餐配置（与后端保持一致）
const PACKAGES = {
  // --- 标准套餐 ---
  'M': { days: 30, name: '月套餐' },
  'Q': { days: 90, name: '季度套餐' },
  'H': { days: 180, name: '半年套餐' },

  // --- PRO套餐 ---
  'PM': { days: 30, name: '月度PRO套餐' },
  'PQ': { days: 90, name: '季度PRO套餐' },
  'PH': { days: 180, name: '半年PRO套餐' },

  // --- 特殊套餐 ---
  'PT': { days: 1, name: '测试套餐' }
} as const

// 用户VIP状态类型定义
interface UserVipStatus {
  isVip: boolean
  isActive: boolean  // 是否为活跃VIP
  expireAt: number
  remainingDays: number | null
  quotaDisplay: string
  validityDisplay: string
  statusType: 'active' | 'expired' | 'inactive'
  type?: string
  remainingTime?: string
  // 新增配额相关字段
  isLegacyUser?: boolean     // 是否为老用户
  usagePercentage?: number   // 使用百分比
  quotaChars?: number        // 总配额
  usedChars?: number         // 已用配额
  remainingChars?: number    // 剩余配额
}

// 标准会员套餐
const standardPackages = [
  {
    id: "monthly",
    name: "月度会员",
    price: 25,
    originalPrice: 35,
    discount: "省¥10",
    quota: "无限次数",
    validity: 30,
    features: ["无限字符转换", "高级参数调节", "高清音质输出"],
    popular: false,
    icon: Zap,
    color: "blue",
    type: "standard",
    limitedOffer: true
  },
  {
    id: "quarterly",
    name: "季度会员",
    price: 55,
    originalPrice: 105,
    discount: "省¥50",
    quota: "无限次数",
    validity: 90,
    features: ["无限字符转换", "高级参数调节", "高清音质输出"],
    popular: true,
    icon: Star,
    color: "purple",
    type: "standard",
    limitedOffer: true
  },
  {
    id: "halfyear",
    name: "半年会员",
    price: 99,
    originalPrice: 210,
    discount: "省¥111",
    quota: "无限次数",
    validity: 180,
    features: ["无限字符转换", "高级参数调节", "高清音质输出"],
    popular: false,
    icon: Crown,
    color: "gold",
    type: "standard",
    limitedOffer: true
  },
]

// PRO会员套餐
const proPackages = [
  {
    id: "monthly-pro",
    name: "月Pro",
    price: 45,
    originalPrice: 60,
    discount: "省¥15",
    quota: "无限次数",
    validity: 30,
    features: ["无限字符转换", "高级参数调节", "高清音质输出", "多人对话模式"],
    popular: false,
    icon: Sparkles,
    color: "gradient-blue",
    type: "pro",
    badge: "PRO",
    limitedOffer: true
  },
  {
    id: "quarterly-pro",
    name: "季度Pro",
    price: 120,
    originalPrice: 180,
    discount: "省¥60",
    quota: "无限次数",
    validity: 90,
    features: ["无限字符转换", "高级参数调节", "高清音质输出", "多人对话模式"],
    popular: true,
    icon: Diamond,
    color: "gradient-purple",
    type: "pro",
    badge: "PRO",
    limitedOffer: true
  },
  {
    id: "halfyear-pro",
    name: "半年Pro",
    price: 220,
    originalPrice: 360,
    discount: "省¥140",
    quota: "无限次数",
    validity: 180,
    features: ["无限字符转换", "高级参数调节", "高清音质输出", "多人对话模式"],
    popular: false,
    icon: Award,
    color: "gradient-gold",
    type: "pro",
    badge: "PRO",
    limitedOffer: true
  },
]

export default function RechargeCenter() {
  const { toast } = useToast()
  const [cardCode, setCardCode] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isPageLoaded, setIsPageLoaded] = useState(false)
  const [activeTab, setActiveTab] = useState("standard")
  const [isClient, setIsClient] = useState(false)

  // 获取当前活动的套餐
  const getCurrentPackages = () => {
    return activeTab === "standard" ? standardPackages : proPackages
  }

  // 统一的用户状态管理
  const [userStatus, setUserStatus] = useState<UserVipStatus>({
    isVip: false,
    isActive: false,
    expireAt: 0,
    remainingDays: null,
    quotaDisplay: "获取中...",
    validityDisplay: "获取中...",
    statusType: 'inactive',
    // 初始化配额相关字段
    isLegacyUser: false,      // 默认为新用户，避免套餐显示错误
    usagePercentage: 0,
    quotaChars: 0,
    usedChars: 0,
    remainingChars: 0
  })

  // 获取套餐天数的辅助函数
  const getPackageDays = (packageType?: string): number => {
    if (!packageType || !(packageType in PACKAGES)) {
      return 30 // 默认30天
    }
    return PACKAGES[packageType as keyof typeof PACKAGES].days
  }

  // 判断是否为PRO会员的辅助函数
  const isProMember = (packageType?: string): boolean => {
    return packageType ? packageType.startsWith('P') : false
  }

  // 获取会员类型显示信息的辅助函数
  const getMembershipInfo = (packageType?: string) => {
    if (!packageType) {
      return {
        tier: 'none',
        displayName: '未开通',
        badge: null,
        colors: {
          primary: 'text-gray-600',
          secondary: 'text-gray-500',
          bg: 'bg-gray-50',
          border: 'border-gray-200'
        }
      }
    }

    const isPro = isProMember(packageType)
    const packageName = PACKAGES[packageType as keyof typeof PACKAGES]?.name || '未知套餐'

    if (packageType === 'T') {
      return {
        tier: 'test',
        displayName: '测试套餐',
        badge: 'TEST',
        colors: {
          primary: 'text-orange-600',
          secondary: 'text-orange-500',
          bg: 'bg-orange-50',
          border: 'border-orange-200'
        }
      }
    }

    if (isPro) {
      return {
        tier: 'pro',
        displayName: packageName,
        badge: 'PRO',
        colors: {
          primary: 'text-purple-600',
          secondary: 'text-purple-500',
          bg: 'bg-gradient-to-r from-purple-50 to-pink-50',
          border: 'border-purple-200'
        }
      }
    }

    return {
      tier: 'standard',
      displayName: packageName,
      badge: 'VIP',
      colors: {
        primary: 'text-blue-600',
        secondary: 'text-blue-500',
        bg: 'bg-blue-50',
        border: 'border-blue-200'
      }
    }
  }

  // 计算进度百分比的辅助函数 - 修改为显示剩余时间进度
  const calculateProgress = () => {
    if (!userStatus.isVip || !userStatus.expireAt || userStatus.remainingDays === null) {
      return 0
    }

    const totalDays = getPackageDays(userStatus.type)
    // 修改逻辑：进度条显示剩余时间比例（从100%递减到0%）
    const progress = Math.max(0, Math.min(100, (userStatus.remainingDays / totalDays) * 100))

    return progress
  }



  // 获取套餐配额显示（统一显示具体配额，不区分用户类型）
  const getPackageQuotaDisplay = (packageId: string) => {
    // 套餐配额配置（与后端PACKAGES保持一致）
    const quotaConfig = {
      'monthly': 80000,      // 月套餐：8万字符
      'quarterly': 250000,   // 季度套餐：25万字符
      'halfyear': 550000,    // 半年套餐：55万字符
      'monthly-pro': 250000, // 月度PRO：25万字符
      'quarterly-pro': 800000, // 季度PRO：80万字符
      'halfyear-pro': 2000000  // 半年PRO：200万字符
    }

    const quota = quotaConfig[packageId as keyof typeof quotaConfig]
    return quota ? `${(quota / 10000).toFixed(0)}万字符` : "未知配额"
  }

  // 获取套餐功能描述（统一显示具体配额，不区分用户类型）
  const getPackageFeatures = (originalFeatures: string[], packageId: string) => {
    const features = [...originalFeatures]

    // 更新第一个功能描述（字符转换相关）
    if (features[0] === "无限字符转换") {
      const quotaDisplay = getPackageQuotaDisplay(packageId)
      features[0] = `${quotaDisplay}字符转换`
    }

    return features
  }

  // 统一的状态更新函数
  const updateUserStatus = (data: UserQuotaResponse) => {
    const now = Date.now()
    const isActive = data.isVip && now < data.expireAt
    const remaining = data.expireAt > 0 ? Math.max(0, Math.ceil((data.expireAt - now) / (1000 * 60 * 60 * 24))) : null

    let statusType: 'active' | 'expired' | 'inactive' = 'inactive'
    let quotaDisplay = "未开通"
    let validityDisplay = "未开通"

    if (isActive) {
      statusType = 'active'

      // 【新增】根据用户类型显示不同的配额信息
      if (data.isLegacyUser) {
        // 老用户：显示无限字符
        quotaDisplay = "无限字符"
      } else {
        // 新用户：显示具体配额信息
        const remaining = data.remainingChars || 0
        const total = data.quotaChars || 0
        quotaDisplay = `${remaining.toLocaleString()} / ${total.toLocaleString()} 字符`
      }

      // 使用固定格式避免本地化差异 - 解决水合失败问题
      const expireDate = new Date(data.expireAt)
      validityDisplay = `${expireDate.getFullYear()}-${String(expireDate.getMonth() + 1).padStart(2, '0')}-${String(expireDate.getDate()).padStart(2, '0')}`
    } else if (data.isVip && data.expireAt > 0) {
      statusType = 'expired'
      quotaDisplay = "已过期"
      validityDisplay = "已过期"
    }

    setUserStatus({
      isVip: data.isVip,
      isActive,
      expireAt: data.expireAt,
      remainingDays: remaining,
      quotaDisplay,
      validityDisplay,
      statusType,
      type: data.type,
      remainingTime: data.remainingTime || undefined,
      // 新增配额相关字段
      isLegacyUser: data.isLegacyUser,
      usagePercentage: data.usagePercentage,
      quotaChars: data.quotaChars,
      usedChars: data.usedChars,
      remainingChars: data.remainingChars
    })
  }

  // 客户端挂载状态管理 - 解决水合失败问题
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const initPage = async () => {
      // 检查登录状态
      const isLoggedIn = auth.isLoggedIn()
      if (!isLoggedIn) {
        window.location.href = "/login"
        return
      }

      try {
        // 使用AuthService获取用户VIP信息，内置token刷新机制
        const data = await auth.getUserQuota()

        // 使用统一的状态更新函数
        updateUserStatus(data)
      } catch (error: any) {
        console.error('获取VIP信息失败:', error)

        // 【关键修改】使用统一的错误处理逻辑
        const errorUI = getErrorUIState(error)

        // 根据错误类型更新UI
        setUserStatus(prev => ({
          ...prev,
          quotaDisplay: errorUI.statusDisplay,
          validityDisplay: errorUI.statusDisplay,
          // 确保错误状态下也有正确的默认值
          isLegacyUser: false,  // 错误状态下默认为新用户，避免显示错误
        }))

        // 根据错误类型显示正确的提示
        toast({
          title: errorUI.toastTitle,
          description: errorUI.toastDescription,
          variant: errorUI.variant,
        })
      }

      setIsPageLoaded(true)
    }

    initPage()
  }, [])

  // 实时更新剩余时间
  useEffect(() => {
    if (!userStatus.isVip || !userStatus.expireAt) return

    // 每分钟更新一次剩余时间
    const interval = setInterval(() => {
      const now = Date.now()
      const remaining = Math.max(0, Math.ceil((userStatus.expireAt - now) / (1000 * 60 * 60 * 24)))

      // 如果剩余天数发生变化，更新状态
      if (remaining !== userStatus.remainingDays) {
        setUserStatus(prev => ({
          ...prev,
          remainingDays: remaining
        }))

        // 如果已过期，更新状态并通知用户
        if (remaining === 0 && userStatus.isActive) {
          setUserStatus(prev => ({
            ...prev,
            isActive: false,
            quotaDisplay: "已过期",
            validityDisplay: "已过期",
            statusType: 'expired'
          }))

          // 通知用户会员已过期
          toast({
            title: "会员已过期",
            description: "您的会员已过期，请续费以继续使用",
            variant: "destructive",
          })
        }
      }
    }, 60000) // 每分钟检查一次

    return () => clearInterval(interval)
  }, [userStatus.isVip, userStatus.expireAt, userStatus.remainingDays, userStatus.isActive, toast])

  // 刷新用户信息的函数
  const refreshUserInfo = async () => {
    try {
      // 使用AuthService获取用户信息，内置token刷新机制
      const data = await auth.getUserQuota()
      updateUserStatus(data)
    } catch (error: any) {
      console.error('刷新用户信息失败:', error)

      let toastDescription = "获取最新用户信息失败，请手动刷新页面"

      // 识别认证错误
      if (error.message?.includes('登录') ||
          error.message?.includes('refresh') ||
          error.message?.includes('401') ||
          error.message?.includes('No refresh token available')) {
        toastDescription = "会话已过期，正在跳转到登录页面..."
      }

      toast({
        title: "刷新失败",
        description: toastDescription,
        variant: "destructive",
      })
    }
  }

  const handleCardRecharge = async () => {
    if (!cardCode.trim()) {
      toast({
        title: "请输入卡密",
        description: "请输入有效的充值卡密",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      // 使用cardService进行卡密充值，内置token刷新机制
      await cardService.useCard(cardCode.trim())

      // 显示成功提示
      toast({
        title: "充值成功！",
        description: "会员权限已激活，正在更新信息...",
      })

      setCardCode("")

      // 局部刷新用户信息，而不是整页刷新
      await refreshUserInfo()
    } catch (error: any) {
      console.error('充值失败:', error)

      // 【关键修改】使用统一的错误处理逻辑
      const errorUI = getErrorUIState(error)

      toast({
        title: errorUI.toastTitle,
        description: errorUI.toastDescription,
        variant: errorUI.variant,
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  // 套餐卡片渲染组件
  const PackageCard = ({ pkg, index }: { pkg: any, index: number }) => {
    const IconComponent = pkg.icon
    const isPro = pkg.type === "pro"

    // PRO套餐的渐变色配置
    const getProGradientClasses = (color: string) => {
      switch (color) {
        case "gradient-blue":
          return {
            bg: "bg-gradient-to-br from-blue-500/10 to-cyan-500/10",
            border: "border-blue-300/50",
            icon: "bg-gradient-to-r from-blue-100 to-cyan-100",
            iconColor: "text-blue-600",
            badge: "bg-gradient-to-r from-blue-500 to-cyan-500"
          }
        case "gradient-purple":
          return {
            bg: "bg-gradient-to-br from-purple-500/10 to-pink-500/10",
            border: "border-purple-300/50",
            icon: "bg-gradient-to-r from-purple-100 to-pink-100",
            iconColor: "text-purple-600",
            badge: "bg-gradient-to-r from-purple-500 to-pink-500"
          }
        case "gradient-gold":
          return {
            bg: "bg-gradient-to-br from-yellow-500/10 to-orange-500/10",
            border: "border-yellow-300/50",
            icon: "bg-gradient-to-r from-yellow-100 to-orange-100",
            iconColor: "text-orange-600",
            badge: "bg-gradient-to-r from-yellow-500 to-orange-500"
          }
        default:
          return {
            bg: "bg-gradient-to-br from-gray-500/10 to-slate-500/10",
            border: "border-gray-300/50",
            icon: "bg-gradient-to-r from-gray-100 to-slate-100",
            iconColor: "text-gray-600",
            badge: "bg-gradient-to-r from-gray-500 to-slate-500"
          }
      }
    }

    const proStyles = isPro ? getProGradientClasses(pkg.color) : null

    return (
      <Card
        key={pkg.id}
        className={`relative border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden group h-full flex flex-col ${
          pkg.popular ? "ring-2 ring-purple-400 ring-opacity-50" : ""
        } ${isPro ? `${proStyles?.bg} ${proStyles?.border} border-2` : ""}`}
        style={{ animationDelay: `${index * 200}ms` }}
      >
        {/* 限时优惠标识 - 左上角 */}
        {pkg.limitedOffer && (
          <div className={`absolute top-0 left-0 text-white px-3 py-1 text-xs font-bold rounded-br-lg shadow-lg animate-pulse ${
            isPro
              ? "bg-gradient-to-r from-amber-500 to-orange-500"
              : "bg-gradient-to-r from-orange-500 to-red-500"
          }`}>
            <span className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              限时优惠
            </span>
          </div>
        )}

        {/* PRO徽章 */}
        {isPro && pkg.badge && (
          <div className={`absolute top-0 right-0 ${proStyles?.badge} text-white px-4 py-1 text-sm font-bold rounded-bl-lg shadow-lg`}>
            {pkg.badge}
          </div>
        )}

        {/* 推荐标签 */}
        {pkg.popular && !isPro && (
          <div className="absolute top-0 right-0 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 text-sm font-bold rounded-bl-lg">
            推荐
          </div>
        )}

        <div className={`absolute inset-0 ${isPro ? 'bg-gradient-to-r from-purple-500/5 to-pink-500/5' : 'bg-gradient-to-r from-purple-500/5 to-pink-500/5'} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />

        <CardHeader className="relative text-center pb-4">
          <div className="flex justify-center mb-4">
            <div
              className={`p-4 rounded-2xl ${
                isPro
                  ? proStyles?.icon
                  : pkg.color === "blue"
                    ? "bg-gradient-to-r from-blue-100 to-blue-200"
                    : pkg.color === "purple"
                      ? "bg-gradient-to-r from-purple-100 to-pink-200"
                      : "bg-gradient-to-r from-yellow-100 to-orange-200"
              }`}
            >
              <IconComponent
                className={`w-8 h-8 ${
                  isPro
                    ? proStyles?.iconColor
                    : pkg.color === "blue"
                      ? "text-blue-600"
                      : pkg.color === "purple"
                        ? "text-purple-600"
                        : "text-orange-600"
                }`}
              />
            </div>
          </div>
          <CardTitle className={`text-2xl font-bold mb-4 ${isPro ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent' : 'text-gray-900'}`}>
            {pkg.name}
          </CardTitle>
          <div className="flex flex-col items-center justify-center mb-4">
            <div className="text-5xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">
              ¥{pkg.price}
            </div>
            {pkg.originalPrice && (
              <div className="flex items-center gap-2">
                <span className="text-lg text-gray-400 line-through">¥{pkg.originalPrice}</span>
                <span className="text-sm bg-red-100 text-red-600 px-2 py-1 rounded-full font-bold">
                  {pkg.discount}
                </span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="relative flex flex-col h-full">
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-600">配额</span>
              <span className="font-bold text-emerald-600">
                {getPackageQuotaDisplay(pkg.id)}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-600">有效期</span>
              <span className="font-bold text-gray-900">{pkg.validity} 天</span>
            </div>
          </div>

          <div className="space-y-3 mb-4 flex-grow">
            {getPackageFeatures(pkg.features, pkg.id).map((feature: string, idx: number) => {
              const isHighlightFeature = isPro && feature === "多人对话模式"
              return (
                <div key={idx} className={`flex items-center gap-3 ${isHighlightFeature ? 'bg-gradient-to-r from-purple-50 to-pink-50 p-2 rounded-lg border border-purple-200/50' : ''}`}>
                  {isHighlightFeature ? (
                    <div className="flex items-center justify-center w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                      <Users className="w-3 h-3 text-white" />
                    </div>
                  ) : (
                    <div className={`w-2 h-2 rounded-full ${isPro ? 'bg-gradient-to-r from-purple-400 to-pink-500' : 'bg-gradient-to-r from-emerald-400 to-teal-500'}`} />
                  )}
                  <span className={`text-sm ${
                    isHighlightFeature
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-bold'
                      : 'text-gray-700'
                  }`}>
                    {feature}
                    {isHighlightFeature && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                        NEW
                      </span>
                    )}
                  </span>
                </div>
              )
            })}
          </div>

          <Button
            className={`button-hover-optimized w-full h-12 font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 ${
              pkg.popular || isPro
                ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                : "bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 border border-gray-300"
            }`}
          >
            {pkg.popular || isPro ? "立即购买" : "选择套餐"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  // 预定义的粒子位置和动画参数 - 解决水合失败问题
  const rechargeParticleConfigs = [
    { left: 12, top: 25, duration: 9 },
    { left: 78, top: 40, duration: 11 },
    { left: 35, top: 65, duration: 8 },
    { left: 88, top: 18, duration: 12 },
    { left: 22, top: 75, duration: 10 },
    { left: 68, top: 50, duration: 13 },
    { left: 55, top: 30, duration: 9 },
    { left: 40, top: 85, duration: 11 }
  ];

  const FloatingParticles = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {rechargeParticleConfigs.map((config, i) => (
        <div
          key={i}
          className="absolute w-2 h-2 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full opacity-20 animate-float"
          style={{
            left: `${config.left}%`,
            top: `${config.top}%`,
            animationDelay: `${i * 2}s`,
            animationDuration: `${config.duration}s`,
          }}
        />
      ))}
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-teal-50/20 p-6 relative overflow-hidden">
      {isClient && <FloatingParticles />}

      {/* Animated Background Elements */}
      <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-emerald-200/20 to-teal-200/20 rounded-full blur-3xl animate-pulse" />
      <div
        className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-teal-200/20 to-cyan-200/20 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "2s" }}
      />

      <div
        className={`max-w-7xl mx-auto transition-all duration-1000 ${isPageLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
      >
        {/* Header */}
        <div className="mb-2">
          <div className="flex items-center gap-4 mb-6">
            <Button
              onClick={() => window.history.back()}
              variant="outline"
              size="lg"
              className="w-12 h-12 rounded-full border-2 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-emerald-800 to-teal-800 bg-clip-text text-transparent">
                充值中心
              </h1>
              <div className="h-1 w-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full mt-2" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - User Info & Card Recharge */}
          <div className="lg:col-span-1 space-y-4">
            {/* 配额信息 */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <CardHeader className="relative pb-5">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="p-1.5 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-lg">
                    <Zap className="w-4 h-4 text-emerald-600" />
                  </div>
                  当前配额
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <div className="text-center">
                  {/* 会员类型徽章 - 始终显示 */}
                  {(() => {
                    // 如果有活跃会员状态，显示具体套餐信息
                    if (userStatus.isActive && userStatus.type) {
                      const memberInfo = getMembershipInfo(userStatus.type)
                      return (
                        <div className="mb-3 flex justify-center">
                          <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-bold border ${memberInfo.colors.bg} ${memberInfo.colors.border}`}>
                            {memberInfo.tier === 'pro' && (
                              <div className="flex items-center justify-center w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                                <Crown className="w-2.5 h-2.5 text-white" />
                              </div>
                            )}
                            {memberInfo.tier === 'standard' && (
                              <div className="flex items-center justify-center w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full">
                                <Star className="w-2.5 h-2.5 text-white" />
                              </div>
                            )}
                            {memberInfo.tier === 'test' && (
                              <div className="flex items-center justify-center w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full">
                                <Clock className="w-2.5 h-2.5 text-white" />
                              </div>
                            )}
                            <span className={memberInfo.colors.primary}>
                              {memberInfo.displayName}
                            </span>
                            {memberInfo.badge && (
                              <span className={`px-1.5 py-0.5 text-xs font-bold rounded ${
                                memberInfo.tier === 'pro'
                                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                                  : memberInfo.tier === 'test'
                                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                                  : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white'
                              }`}>
                                {memberInfo.badge}
                              </span>
                            )}
                          </div>
                        </div>
                      )
                    }

                    // 如果没有活跃会员，显示未开通状态
                    return (
                      <div className="mb-3 flex justify-center">
                        <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-bold border bg-gray-50 border-gray-200">
                          <div className="flex items-center justify-center w-4 h-4 bg-gray-400 rounded-full">
                            <Zap className="w-2.5 h-2.5 text-white" />
                          </div>
                          <span className="text-gray-600">未开通会员</span>
                          <span className="px-1.5 py-0.5 text-xs font-bold rounded bg-gray-400 text-white">
                            FREE
                          </span>
                        </div>
                      </div>
                    )
                  })()}

                  <div className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-1">
                    {userStatus.quotaDisplay}
                  </div>
                  <p className="text-gray-600 text-sm">配音权限</p>
                  <div className="mt-3 w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full transition-all duration-1000"
                      style={{
                        width: userStatus.isActive
                          ? (userStatus.isLegacyUser ? "100%" : `${Math.max(5, 100 - (userStatus.usagePercentage || 0))}%`)
                          : "0%"
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    {userStatus.isActive
                      ? (() => {
                          if (userStatus.isLegacyUser) {
                            // 老用户：显示无限制使用
                            const memberInfo = getMembershipInfo(userStatus.type)
                            return memberInfo.tier === 'pro'
                              ? "PRO会员专享无限字符"
                              : memberInfo.tier === 'test'
                              ? "测试套餐无限字符"
                              : "标准会员无限字符"
                          } else {
                            // 新用户：显示配额使用情况
                            const used = userStatus.usedChars || 0
                            const percentage = userStatus.usagePercentage || 0
                            return `已使用 ${used.toLocaleString()} 字符 (${percentage.toFixed(1)}%)`
                          }
                        })()
                      : "请购买套餐开通会员"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 有效期信息 */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <CardHeader className="relative">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl">
                    <Clock className="w-5 h-5 text-blue-600" />
                  </div>
                  有效期
                </CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <div className="space-y-4">
                  {/* Main Validity Display */}
                  <div className="text-center">
                    <div className="relative inline-block">
                      <div className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-1">
                        {userStatus.validityDisplay}
                      </div>
                      {userStatus.isActive && (
                        <div className="absolute -top-6 -right-6 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
                      )}
                    </div>
                  </div>

                  {/* Visual Timeline Progress - 只在有有效期时显示 */}
                  {userStatus.remainingDays !== null && userStatus.isActive && (
                    <div className="relative">
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                        <span className="font-semibold text-blue-600">剩余 {userStatus.remainingDays} 天</span>
                        <span>到期日期</span>
                      </div>

                      {/* Progress Bar */}
                      <div className="relative w-full bg-gray-200 rounded-full h-2.5 overflow-hidden mb-2">
                        <div
                          className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-500 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${calculateProgress()}%` }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />

                        {/* Progress Indicator */}
                        <div
                          className="absolute top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-1000"
                          style={{ left: `${Math.max(1.75, Math.min(96.5, calculateProgress() - 1.75))}%` }}
                        >
                          <div className="absolute inset-0.5 bg-blue-500 rounded-full animate-pulse" />
                        </div>
                      </div>

                      {/* Timeline Markers */}
                      <div className="flex justify-between">
                        <div className="flex flex-col items-center">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mb-0.5 animate-pulse" />
                          <span className="text-xs text-blue-600 font-semibold">今天</span>
                        </div>
                        <div className="flex flex-col items-center">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mb-0.5" />
                          <span className="text-xs text-gray-500">{userStatus.validityDisplay}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Status Cards Grid */}
                  <div className="grid grid-cols-2 gap-2">
                    <div className={`p-2.5 rounded-lg border ${
                      userStatus.statusType === 'active'
                        ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
                        : userStatus.statusType === 'expired'
                        ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200/50'
                        : 'bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200/50'
                    }`}>
                      <div className="flex items-center justify-center gap-1.5 mb-0.5">
                        <div className={`w-1.5 h-1.5 rounded-full ${
                          userStatus.statusType === 'active'
                            ? 'bg-green-500'
                            : userStatus.statusType === 'expired'
                            ? 'bg-red-500'
                            : 'bg-gray-400'
                        }`} />
                        <span className={`text-xs font-semibold ${
                          userStatus.statusType === 'active'
                            ? 'text-green-700'
                            : userStatus.statusType === 'expired'
                            ? 'text-red-700'
                            : 'text-gray-700'
                        }`}>活跃状态</span>
                      </div>
                      <div className={`text-base font-bold text-center ${
                        userStatus.statusType === 'active'
                          ? 'text-green-800'
                          : userStatus.statusType === 'expired'
                          ? 'text-red-800'
                          : 'text-gray-800'
                      }`}>
                        {userStatus.statusType === 'active' ? '正常' : userStatus.statusType === 'expired' ? '已过期' : '未开通'}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-2.5 rounded-lg border border-blue-200/50">
                      <div className="flex items-center justify-center gap-1.5 mb-0.5">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse" />
                        <span className="text-xs font-semibold text-blue-700">剩余时间</span>
                      </div>
                      <div className="text-base font-bold text-blue-800 text-center">
                        {userStatus.remainingDays !== null
                          ? userStatus.remainingDays > 0
                            ? `${userStatus.remainingDays}天`
                            : '已过期'
                          : '未开通'
                        }
                      </div>
                    </div>
                  </div>


                </div>
              </CardContent>
            </Card>

            {/* 卡密充值区域 */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <CardHeader className="relative pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="p-1.5 bg-gradient-to-r from-orange-100 to-red-100 rounded-lg">
                    <CreditCard className="w-4 h-4 text-orange-600" />
                  </div>
                  卡密充值
                </CardTitle>
              </CardHeader>
              <CardContent className="relative space-y-3">
                <div>
                  <Input
                    value={cardCode}
                    onChange={(e) => setCardCode(e.target.value)}
                    placeholder="请输入充值卡密"
                    className="w-full h-10 border-2 border-gray-200 focus:border-orange-400 focus:ring-2 focus:ring-orange-50 transition-all duration-300"
                  />
                </div>
                <Button
                  onClick={handleCardRecharge}
                  disabled={!cardCode.trim() || isSubmitting}
                  className="w-full h-10 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      充值中...
                    </div>
                  ) : (
                    "立即充值"
                  )}
                </Button>
                <p className="text-xs text-gray-500 text-center">请确保卡密正确，充值后立即生效</p>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Package Plans */}
          <div className="lg:col-span-2 flex flex-col">
            <div className="mb-6">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                套餐说明
              </h2>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-2 shadow-sm">
                <p className="text-gray-700 text-base leading-relaxed">
                  <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  可提供测试，如需购买卡密请联系
                  <span className="animate-rainbow font-bold text-lg text-transparent bg-clip-text bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-cyan-500 to-lime-500 mx-1">sunshine-12-06</span>
                  微信，购买前请先仔细查看套餐信息。
                </p>
              </div>
            </div>

            {/* 套餐选项卡 */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6 h-12 bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg">
                <TabsTrigger
                  value="standard"
                  className="text-base font-semibold data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white transition-all duration-300"
                >
                  标准会员
                </TabsTrigger>
                <TabsTrigger
                  value="pro"
                  className="text-base font-semibold data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white transition-all duration-300"
                >
                  <span className="flex items-center gap-2">
                    PRO会员
                    <Sparkles className="w-4 h-4" />
                  </span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="standard" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {standardPackages.map((pkg, index) => (
                    <PackageCard key={pkg.id} pkg={pkg} index={index} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="pro" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {proPackages.map((pkg, index) => (
                    <PackageCard key={pkg.id} pkg={pkg} index={index} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        .animate-float {
          animation: float 8s ease-in-out infinite;
          will-change: transform;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        .animate-shimmer {
          animation: shimmer 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
