"use client"

import React from 'react';
import { useConditionalAnimation } from '@/hooks/useAnimationVisibility';

// 预定义的粒子位置和动画参数 - 从 page.tsx 移出
const particleConfigs = [
  { left: 15, top: 20, duration: 10 },
  { left: 75, top: 35, duration: 12 },
  { left: 45, top: 60, duration: 9 },
  { left: 85, top: 15, duration: 11 },
  { left: 25, top: 80, duration: 8 },
  { left: 65, top: 45, duration: 13 }
];

// 使用 React.memo 包裹粒子动画组件 - 防止父组件重渲染时重新渲染
export const FloatingParticles = React.memo(function FloatingParticles() {
  console.log("FloatingParticles is rendering"); // 性能优化后，这个只会在页面加载时打印一次

  const { animationClass, elementRef } = useConditionalAnimation('animate-float');

  return (
    <div ref={elementRef} className="absolute inset-0 overflow-hidden pointer-events-none">
      {particleConfigs.map((config, i) => (
        <div
          key={i}
          className={`animate-optimized absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 ${animationClass}`}
          style={{
            left: `${config.left}%`,
            top: `${config.top}%`,
            animationDelay: `${i * 2}s`,
            animationDuration: `${config.duration}s`,
          }}
        />
      ))}
    </div>
  );
});

// 使用 React.memo 包裹背景光晕组件 - 防止父组件重渲染时重新渲染
export const AnimatedBackgroundBlobs = React.memo(function AnimatedBackgroundBlobs() {
  console.log("AnimatedBackgroundBlobs is rendering"); // 性能优化后，这个只会在页面加载时打印一次

  const { animationClass, elementRef } = useConditionalAnimation('animate-pulse');

  return (
    <div ref={elementRef}>
      {/* 右上角背景光晕 */}
      <div className={`animate-optimized absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-blue-200/50 to-purple-200/40 rounded-full blur-3xl ${animationClass}`} />

      {/* 左下角背景光晕 - 带延迟 */}
      <div
        className={`animate-optimized absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-200/50 to-pink-200/40 rounded-full blur-3xl ${animationClass}`}
        style={{ animationDelay: "2s" }}
      />
    </div>
  );
});
