"use client"

import { useEffect, useRef, useState } from 'react';

/**
 * 动画可见性控制Hook
 * 使用Intersection Observer检测元素是否在视口中
 * 当元素不可见时暂停动画，减少不必要的计算
 */
export function useAnimationVisibility(options?: IntersectionObserverInit) {
  const [isVisible, setIsVisible] = useState(true); // 默认可见，避免初始闪烁
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 创建Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsVisible(entry.isIntersecting);
      },
      {
        // 默认配置：元素进入视口10%时开始动画
        threshold: 0.1,
        // 提前50px开始检测，确保动画及时启动
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [options]);

  return { isVisible, elementRef };
}

/**
 * 动画类名控制Hook
 * 根据可见性自动添加/移除动画类名
 */
export function useConditionalAnimation(
  animationClass: string,
  options?: IntersectionObserverInit
) {
  const { isVisible, elementRef } = useAnimationVisibility(options);
  
  // 根据可见性返回动画类名
  const getAnimationClass = () => {
    return isVisible ? animationClass : '';
  };

  return { 
    animationClass: getAnimationClass(), 
    isVisible, 
    elementRef 
  };
}

/**
 * 批量动画控制Hook
 * 用于控制多个动画元素的可见性
 */
export function useBatchAnimationControl() {
  const [globalAnimationEnabled, setGlobalAnimationEnabled] = useState(true);

  // 当页面不可见时暂停所有动画
  useEffect(() => {
    const handleVisibilityChange = () => {
      setGlobalAnimationEnabled(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return {
    globalAnimationEnabled,
    setGlobalAnimationEnabled,
  };
}
