import requests
import json
import os
import time
from mutagen import File as MutagenFile # 用于读取音频时长 (需要先 pip install mutagen)
import wave # 导入内置的wave模块
import random # 用于随机选择请求头

# --- API 配置 ---
API_URL = "https://api.elevenlabs.io/v1/speech-to-text"
PARAMS = {
    "allow_unauthenticated": "1" # 若使用API密钥，可移除此行并在headers中设置
}

# --- 内置的默认请求头列表 ---
# 用于模拟不同浏览器的请求
DEFAULT_USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:126.0) Gecko/20100101 Firefox/126.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:125.0) Gecko/20100101 Firefox/125.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:124.0) Gecko/20100101 Firefox/124.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 14; Pixel 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; SM-S918U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.70 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edg/124.0.2478.80",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/123.0.2420.97",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (iPad; CPU OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 12; SM-A525F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_7_10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.4 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 11; CPH2239) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/121.0.2277.128",
    "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.0 Safari/537.36"
]
DEFAULT_ACCEPT_LANGUAGES = [
    "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6",
    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,ja;q=0.5",
    "en-GB,en;q=0.9,en-US;q=0.8,de;q=0.7,fr;q=0.6",
    "ja-JP,ja;q=0.9,en-US;q=0.8,en;q=0.7",
    "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,fr;q=0.6",
    "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,es;q=0.6",
    "es-ES,es;q=0.9,en;q=0.8,pt;q=0.7",
    "ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6",
    "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
    "it-IT,it;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,es;q=0.6",
    "en-CA,en;q=0.9,fr-CA;q=0.8",
    "en-AU,en;q=0.9,en-GB;q=0.8",
    "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6",
    "ar-SA,ar;q=0.9,en-US;q=0.8,en;q=0.7",
    "hi-IN,hi;q=0.9,en-US;q=0.8,en;q=0.7",
    "nl-NL,nl;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "sv-SE,sv;q=0.9,en-US;q=0.8,en;q=0.7,fi;q=0.6",
    "fi-FI,fi;q=0.9,en-US;q=0.8,en;q=0.7,sv;q=0.6",
    "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
    "cs-CZ,cs;q=0.9,en-US;q=0.8,en;q=0.7,sk;q=0.6",
    "hu-HU,hu;q=0.9,en-US;q=0.8,en;q=0.7,de;q=0.6",
    "el-GR,el;q=0.9,en-US;q=0.8,en;q=0.7",
    "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7"
]

# --- 函数定义 ---
def get_audio_info(audio_file_path):
    """获取并显示音频文件的时长和大小。"""
    try:
        file_size_bytes = os.path.getsize(audio_file_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        print(f"  文件大小: {file_size_mb:.2f} MB")

        duration_seconds = None
        # 优先使用 mutagen 获取时长
        audio_info = MutagenFile(audio_file_path)
        if audio_info and hasattr(audio_info, 'info') and hasattr(audio_info.info, 'length'):
            duration_seconds = audio_info.info.length
        # 如果是 .wav 文件且 mutagen 未能获取时长，尝试使用 wave 模块
        elif audio_file_path.lower().endswith(".wav"):
            print("  Mutagen未能获取WAV时长, 尝试使用wave模块...")
            try:
                with wave.open(audio_file_path, 'rb') as wf:
                    frames = wf.getnframes()
                    rate = wf.getframerate()
                    if rate > 0:
                        duration_seconds = frames / float(rate)
                    else:
                        print("  警告：WAV 文件帧率无效 (wave模块)。")
            except Exception as e_wave:
                print(f"  使用wave模块读取WAV时长错误: {e_wave}")

        if duration_seconds is not None:
            minutes = int(duration_seconds // 60)
            seconds = int(duration_seconds % 60)
            milliseconds = int((duration_seconds - (minutes * 60) - seconds) * 1000)
            print(f"  音频时长: {minutes:02d}分{seconds:02d}秒{milliseconds:03d}毫秒 ({duration_seconds:.3f}秒)")
        else:
            print("  警告：未能获取音频时长。")

    except Exception as e:
        print(f"  获取音频信息时发生错误: {e}")

def transcribe_audio_elevenlabs(audio_file_path, num_speakers_param=None, tag_audio_events_param=True):
    """
    上传音频文件到ElevenLabs进行转录。
    `diarize` 参数在函数内部固定为 True。
    """
    if not os.path.exists(audio_file_path):
        print(f"错误：音频文件 '{audio_file_path}' 未找到。")
        return None

    print(f"\n正在处理文件: {os.path.basename(audio_file_path)}")
    get_audio_info(audio_file_path)

    # 构建请求头
    headers = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": random.choice(DEFAULT_ACCEPT_LANGUAGES),
        "origin": "https://elevenlabs.io",
        "referer": "https://elevenlabs.io/",
        "user-agent": random.choice(DEFAULT_USER_AGENTS),
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        # "xi-api-key": "YOUR_ELEVENLABS_API_KEY", # 如果使用API密钥，取消此行注释并填入密钥
    }

    # 构建API请求的表单数据
    payload_data = {
        "model_id": "scribe_v1",
        "tag_audio_events": tag_audio_events_param,
        "diarize": True  # 说话人分离固定为启用
    }
    if num_speakers_param is not None: # 如果指定了说话人数量 (1-32)
        payload_data["num_speakers"] = num_speakers_param

    try:
        # 打开音频文件并准备上传
        with open(audio_file_path, 'rb') as f_audio:
            file_extension = os.path.splitext(audio_file_path)[1].lower()
            # MIME类型映射表
            mime_type_map = {
                ".mp3": "audio/mpeg", ".wav": "audio/wav", ".flac": "audio/flac",
                ".m4a": "audio/mp4", ".ogg": "audio/ogg", ".opus": "audio/opus",
                ".aac": "audio/aac", ".webm": "audio/webm", ".mp4": "video/mp4",
                ".mov": "video/quicktime"
            }
            mime_type = mime_type_map.get(file_extension, 'application/octet-stream')
            if mime_type == 'application/octet-stream':
                print(f"  警告：未知的音频文件扩展名 '{file_extension}'，使用通用MIME类型 '{mime_type}'。")

            files_data = { "file": (os.path.basename(audio_file_path), f_audio, mime_type) }

            print(f"\n开始上传并请求转录...")
            print(f"  Payload data being sent (excluding file): {payload_data}") # 显示发送的参数
            start_time = time.perf_counter()
            # 发送POST请求到API
            response = requests.post(API_URL, params=PARAMS, headers=headers, data=payload_data, files=files_data, timeout=600) # 10分钟超时
            end_time = time.perf_counter()
            api_call_duration = end_time - start_time
            print(f"转录请求完成，耗时: {api_call_duration:.2f} 秒")

            response.raise_for_status() # 检查HTTP请求是否成功

            response_json = response.json() # 解析JSON响应
            print("成功从API获取并解析JSON响应。")
            return response_json

    # 异常处理
    except requests.exceptions.Timeout:
        print(f"请求超时：API调用在设定的超时时间内未完成。")
    except requests.exceptions.RequestException as e: # 更通用的网络请求错误
        print(f"请求过程中发生网络或HTTP错误: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"  服务器响应状态码: {e.response.status_code}")
            print(f"  服务器响应内容: {e.response.text}")
    except json.JSONDecodeError: # JSON解析错误
        print("错误：无法解析服务器返回的JSON响应。")
        # 尝试打印原始响应文本以便调试
        print(f"  原始响应文本: {response.text if 'response' in locals() else 'N/A'}")
    except Exception as e: # 其他所有未知错误
        print(f"处理过程中发生其他未知错误: {e}")

    return None

# --- 主程序入口 ---
if __name__ == "__main__":
    print("ElevenLabs 音频转录工具")
    print("=========================")

    # 获取用户输入的音频文件路径
    input_audio_path = input("\n请输入音频文件的完整路径: ").strip()

    if not input_audio_path:
        print("未提供音频文件路径，脚本退出。")
    elif not os.path.exists(input_audio_path):
        print(f"错误: 文件 '{input_audio_path}' 不存在，请检查路径。")
    else:
        # 获取说话人数量设置
        num_speakers_to_send = None # 默认由API自动检测
        while True:
            num_speakers_input = input("请输入预计的说话人数量 (0或直接回车则自动检测, 1-32指定数量): ").strip()
            if not num_speakers_input or num_speakers_input == '0': # 0或空输入表示自动检测
                num_speakers_to_send = None
                print("  将由API自动检测说话人数量。")
                break
            try:
                num_speakers_val = int(num_speakers_input)
                if 1 <= num_speakers_val <= 32: # 有效范围1-32
                    num_speakers_to_send = num_speakers_val
                    print(f"  将指定说话人数量为: {num_speakers_to_send}")
                    break
                else: # 超出1-32范围的数字 (且不是0)
                    print("  输入无效。请输入0、直接回车，或输入1到32之间的数字。")
            except ValueError: # 非数字输入
                print("  输入无效。请输入0、直接回车，或输入1到32之间的数字。")

        # 获取是否标记音频事件的设置
        tag_audio_events_to_send = True # 脚本内默认启用
        while True:
            tag_choice = input("是否标记音频事件 (tag_audio_events)? (y/n，直接回车默认为'yes'): ").strip().lower()
            if not tag_choice or tag_choice in ['y', 'yes']:
                tag_audio_events_to_send = True
                print("  将启用音频事件标记 (tag_audio_events=True)。")
                break
            elif tag_choice in ['n', 'no']:
                tag_audio_events_to_send = False
                print("  将禁用音频事件标记 (tag_audio_events=False)。")
                break
            else:
                print("  输入无效，请输入 'y' 或 'n' (或直接回车)。")

        # 说话人分离 (diarize) 固定为启用，不询问用户
        print("  将默认启用说话人分离 (diarize=True)。")

        # 调用转录函数
        full_transcription_data = transcribe_audio_elevenlabs(
            input_audio_path,
            num_speakers_param=num_speakers_to_send,
            tag_audio_events_param=tag_audio_events_to_send
        )

        # 处理并保存转录结果
        if full_transcription_data:
            if not isinstance(full_transcription_data, dict): # 确保返回的是字典
                print("错误：API返回的数据不是预期的JSON对象格式。")
                print("实际收到的数据：", full_transcription_data) # 打印部分原始数据
            else:
                # 生成默认输出文件名
                default_output_filename = f"{os.path.splitext(os.path.basename(input_audio_path))[0]}_transcript.json"
                # 获取用户指定的输出文件名，或使用默认名
                output_json_filename = input(f"请输入保存转录结果的JSON文件名 (直接回车则默认为: {default_output_filename}): ").strip()
                if not output_json_filename:
                    output_json_filename = default_output_filename

                try:
                    # 将JSON数据写入文件
                    with open(output_json_filename, 'w', encoding='utf-8') as outfile:
                        json.dump(full_transcription_data, outfile, indent=4, ensure_ascii=False)
                    print(f"\n完整的转录结果已成功保存到: {os.path.abspath(output_json_filename)}")
                    # 提示用户检查关键字段
                    if "utterances" in full_transcription_data: # diarize固定为True，所以检查utterances
                        print("  说话人分离结果 (utterances) 存在。")
                    else:
                        print("  注意：已请求说话人分离，但响应中未找到 'utterances' 字段。")

                except IOError as e:
                    print(f"保存文件时发生错误: {e}")
        else:
            print("\n未能成功获取或保存转录结果。")