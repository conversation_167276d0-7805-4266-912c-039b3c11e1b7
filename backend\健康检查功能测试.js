#!/usr/bin/env node

/**
 * 健康检查功能测试脚本
 * 测试新实现的代理健康检查功能
 */

// 模拟环境变量
const mockEnv = {
  TTS_PROXY_URLS: "https://tts-proxy-hk-1.aispeak.top,https://tts-proxy-hk-2.aispeak.top,https://tts-proxy-hk-3.aispeak.top,https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app,https://cloudrun-tts-proxy-747917692143.europe-west4.run.app,https://cloudrun-tts-proxy-747917692143.asia-east1.run.app,https://cloudrun-tts-proxy-747917692143.us-west4.run.app,https://m335qyfgcd.execute-api.il-central-1.amazonaws.com,https://2q9k4hf821.execute-api.sa-east-1.amazonaws.com,https://cfq8zznahi.execute-api.ap-southeast-3.amazonaws.com,https://visually-informed-hamster.edgecompute.app,https://ttsproxy-vercel02.aispeak.top,https://ttsproxy-vercel03.aispeak.top,https://ttsproxy.aispeak.top,https://72f99godtj.execute-api.eu-west-3.amazonaws.com,https://4aowvu1vs6.execute-api.us-east-1.amazonaws.com,https://krgl5bu4uf.execute-api.ca-central-1.amazonaws.com,https://whi4og2z6h.execute-api.af-south-1.amazonaws.com,https://vykjc9dtuk.execute-api.me-central-1.amazonaws.com,https://jd3ly39722.execute-api.eu-west-1.amazonaws.com,https://tts-proxy-aqf9cchyfzfaasfp.australiaeast-01.azurewebsites.net,https://k7wydmwft6.execute-api.us-west-2.amazonaws.com,https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com",
  TTS_PROXY_SECRET: "AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9",
  ENABLE_TTS_PROXY: "true",
  TTS_HEALTH_CHECK_ENABLED: "true",
  TTS_HEALTH_CHECK_TIMEOUT: "2000",
  TTS_HEALTH_CHECK_RETRIES: "2", 
  TTS_HEALTH_CHECK_INTERVAL: "2000",
  TTS_HEALTHY_PROXY_TIMEOUT: "60000",
  TTS_PROXY_TIMEOUT: "45000",
  ENABLE_PROXY_DEBUG: "true"
};

// 模拟代理配置函数
function getTTSProxyConfig(env) {
  let proxyUrls = [];
  if (env.TTS_PROXY_URLS) {
    proxyUrls = env.TTS_PROXY_URLS
      .split(',')
      .map(url => url.trim())
      .filter(Boolean);
  }

  return {
    ENABLE_TTS_PROXY: env.ENABLE_TTS_PROXY === 'true',
    TTS_PROXY_URLS: proxyUrls,
    TTS_PROXY_SECRET: env.TTS_PROXY_SECRET || null,
    TTS_PROXY_TIMEOUT: parseInt(env.TTS_PROXY_TIMEOUT || '45000'),
    TTS_HEALTH_CHECK_ENABLED: env.TTS_HEALTH_CHECK_ENABLED !== 'false',
    TTS_HEALTH_CHECK_TIMEOUT: parseInt(env.TTS_HEALTH_CHECK_TIMEOUT || '2000'),
    TTS_HEALTH_CHECK_RETRIES: parseInt(env.TTS_HEALTH_CHECK_RETRIES || '2'),
    TTS_HEALTH_CHECK_INTERVAL: parseInt(env.TTS_HEALTH_CHECK_INTERVAL || '2000'),
    TTS_HEALTHY_PROXY_TIMEOUT: parseInt(env.TTS_HEALTHY_PROXY_TIMEOUT || '60000'),
    ENABLE_PROXY_DEBUG: env.ENABLE_PROXY_DEBUG === 'true'
  };
}

// 健康检查函数（从worker.js复制）
function getProxyHealthUrl(proxyUrl) {
  if (proxyUrl.includes('amazonaws.com')) {
    return proxyUrl + '/api/v1/health';
  } else {
    return proxyUrl + '/api/health';
  }
}

async function checkSingleProxyHealth(proxyUrl, proxyConfig, env) {
  const healthUrl = getProxyHealthUrl(proxyUrl);
  const maxAttempts = proxyConfig.TTS_HEALTH_CHECK_RETRIES;
  const interval = proxyConfig.TTS_HEALTH_CHECK_INTERVAL;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.log(`[HEALTH-CHECK] 🏥 Checking proxy health (attempt ${attempt}/${maxAttempts}): ${proxyUrl}`);
      }
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), proxyConfig.TTS_HEALTH_CHECK_TIMEOUT);
      
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.status === 200) {
        if (proxyConfig.ENABLE_PROXY_DEBUG) {
          console.log(`[HEALTH-CHECK] ✅ Proxy healthy: ${proxyUrl}`);
        }
        return true;
      }
      
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[HEALTH-CHECK] ⚠️ Proxy health check failed (status ${response.status}): ${proxyUrl}`);
      }
    } catch (error) {
      if (proxyConfig.ENABLE_PROXY_DEBUG) {
        console.warn(`[HEALTH-CHECK] ❌ Proxy health check error (attempt ${attempt}/${maxAttempts}): ${proxyUrl} - ${error.message}`);
      }
    }
    
    if (attempt < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }
  
  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.warn(`[HEALTH-CHECK] 💀 Proxy marked as unhealthy after ${maxAttempts} attempts: ${proxyUrl}`);
  }
  return false;
}

async function getHealthyProxies(proxyUrls, proxyConfig, env) {
  if (!proxyConfig.TTS_HEALTH_CHECK_ENABLED || !proxyUrls || proxyUrls.length === 0) {
    return proxyUrls || [];
  }
  
  const healthCheckStartTime = Date.now();
  
  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 🏥 Starting health check for ${proxyUrls.length} proxies...`);
  }
  
  const healthResults = await Promise.all(
    proxyUrls.map(async (proxyUrl, index) => ({
      url: proxyUrl,
      index: index,
      healthy: await checkSingleProxyHealth(proxyUrl, proxyConfig, env)
    }))
  );
  
  const healthyProxies = healthResults
    .filter(result => result.healthy)
    .map(result => result.url);
  
  const healthCheckDuration = Date.now() - healthCheckStartTime;
  
  if (proxyConfig.ENABLE_PROXY_DEBUG) {
    console.log(`[HEALTH-CHECK] 📊 Health check completed in ${healthCheckDuration}ms:`, {
      totalProxies: proxyUrls.length,
      healthyProxies: healthyProxies.length,
      unhealthyProxies: proxyUrls.length - healthyProxies.length,
      healthyUrls: healthyProxies,
      duration: healthCheckDuration
    });
  }
  
  return healthyProxies;
}

// 测试函数
async function testHealthCheck() {
  console.log('🧪 开始测试健康检查功能...\n');
  
  const proxyConfig = getTTSProxyConfig(mockEnv);
  
  console.log('📋 配置信息:');
  console.log(`- 健康检查启用: ${proxyConfig.TTS_HEALTH_CHECK_ENABLED}`);
  console.log(`- 健康检查超时: ${proxyConfig.TTS_HEALTH_CHECK_TIMEOUT}ms`);
  console.log(`- 健康检查重试: ${proxyConfig.TTS_HEALTH_CHECK_RETRIES}次`);
  console.log(`- 重试间隔: ${proxyConfig.TTS_HEALTH_CHECK_INTERVAL}ms`);
  console.log(`- 健康代理超时: ${proxyConfig.TTS_HEALTHY_PROXY_TIMEOUT}ms`);
  console.log(`- 代理数量: ${proxyConfig.TTS_PROXY_URLS.length}\n`);
  
  console.log('🔍 测试健康检查URL生成:');
  proxyConfig.TTS_PROXY_URLS.forEach((url, index) => {
    const healthUrl = getProxyHealthUrl(url);
    console.log(`${index + 1}. ${url} -> ${healthUrl}`);
  });
  console.log('');
  
  console.log('🏥 开始健康检查测试...\n');
  
  const startTime = Date.now();
  const healthyProxies = await getHealthyProxies(proxyConfig.TTS_PROXY_URLS, proxyConfig, mockEnv);
  const totalTime = Date.now() - startTime;
  
  console.log('\n📊 测试结果汇总:');
  console.log(`- 总代理数: ${proxyConfig.TTS_PROXY_URLS.length}`);
  console.log(`- 健康代理数: ${healthyProxies.length}`);
  console.log(`- 不健康代理数: ${proxyConfig.TTS_PROXY_URLS.length - healthyProxies.length}`);
  console.log(`- 总耗时: ${totalTime}ms`);
  console.log(`- 平均每代理耗时: ${(totalTime / proxyConfig.TTS_PROXY_URLS.length).toFixed(0)}ms`);
  
  if (healthyProxies.length > 0) {
    console.log('\n✅ 健康的代理:');
    healthyProxies.forEach((url, index) => {
      console.log(`${index + 1}. ${url}`);
    });
  } else {
    console.log('\n❌ 没有发现健康的代理');
  }
  
  console.log('\n🎯 性能对比:');
  console.log(`- 传统方案 (45秒×3个不可达): 135秒`);
  console.log(`- 健康检查方案 (实际耗时): ${totalTime}ms`);
  if (totalTime < 135000) {
    const saved = 135000 - totalTime;
    const improvement = ((saved / 135000) * 100).toFixed(1);
    console.log(`- 节省时间: ${saved}ms (${improvement}%提升) 🚀`);
  }
  
  console.log('\n✅ 健康检查功能测试完成!');
}

// 运行测试
if (require.main === module) {
  testHealthCheck().catch(console.error);
}

module.exports = {
  testHealthCheck,
  getProxyHealthUrl,
  checkSingleProxyHealth,
  getHealthyProxies,
  getTTSProxyConfig
};
