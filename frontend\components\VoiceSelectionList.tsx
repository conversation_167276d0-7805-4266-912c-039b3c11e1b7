"use client"

import React, { useMemo, useCallback, useState } from 'react';
import { Play, Pause, Heart } from 'lucide-react';

interface Voice {
  id: string;
  name: string;
  description: string;
  preview?: string;
  gender: string;
  language?: string;
}

interface VoiceSelectionListProps {
  filteredVoices: Voice[];
  onSelectVoice: (voiceId: string) => void;
  currentVoiceId?: string | null;
  previewingVoice: string | null;
  handleVoicePreview: (previewUrl: string | null, voiceId: string) => void;
  voiceIconMapping: Record<string, string>;
  voiceIcons: string[];
  listHeightClass?: string;
  showSelectionIndicator?: boolean;
  // 【新增】收藏功能相关属性
  favoriteVoiceIds?: string[];
  onToggleFavorite?: (voiceId: string) => void;
  showFavoriteButton?: boolean;
}

// 🔧 性能优化：自定义比较函数，避免因为函数引用变化导致的重渲染
const arePropsEqual = (prevProps: VoiceSelectionListProps, nextProps: VoiceSelectionListProps) => {
  // 比较基本属性
  if (
    prevProps.currentVoiceId !== nextProps.currentVoiceId ||
    prevProps.previewingVoice !== nextProps.previewingVoice ||
    prevProps.listHeightClass !== nextProps.listHeightClass ||
    prevProps.showSelectionIndicator !== nextProps.showSelectionIndicator ||
    prevProps.showFavoriteButton !== nextProps.showFavoriteButton ||
    prevProps.onSelectVoice !== nextProps.onSelectVoice ||
    prevProps.handleVoicePreview !== nextProps.handleVoicePreview ||
    prevProps.onToggleFavorite !== nextProps.onToggleFavorite
  ) {
    return false;
  }

  // 比较数组长度和内容
  if (
    prevProps.filteredVoices.length !== nextProps.filteredVoices.length ||
    prevProps.favoriteVoiceIds.length !== nextProps.favoriteVoiceIds.length ||
    prevProps.voiceIcons.length !== nextProps.voiceIcons.length
  ) {
    return false;
  }

  // 比较filteredVoices数组的ID
  for (let i = 0; i < prevProps.filteredVoices.length; i++) {
    if (prevProps.filteredVoices[i].id !== nextProps.filteredVoices[i].id) {
      return false;
    }
  }

  // 比较favoriteVoiceIds数组
  for (let i = 0; i < prevProps.favoriteVoiceIds.length; i++) {
    if (prevProps.favoriteVoiceIds[i] !== nextProps.favoriteVoiceIds[i]) {
      return false;
    }
  }

  // 比较voiceIconMapping对象的键值对数量
  const prevKeys = Object.keys(prevProps.voiceIconMapping);
  const nextKeys = Object.keys(nextProps.voiceIconMapping);
  if (prevKeys.length !== nextKeys.length) {
    return false;
  }

  // 比较voiceIconMapping的关键值
  for (const key of prevKeys) {
    if (prevProps.voiceIconMapping[key] !== nextProps.voiceIconMapping[key]) {
      return false;
    }
  }

  return true;
};

// 使用 React.memo 包裹声音选择列表组件 - 防止父组件重渲染时重新渲染
export const VoiceSelectionList = React.memo(function VoiceSelectionList({
  filteredVoices,
  onSelectVoice,
  currentVoiceId,
  previewingVoice,
  handleVoicePreview,
  voiceIconMapping,
  voiceIcons,
  listHeightClass = 'max-h-80',
  showSelectionIndicator = true,
  // 【新增】收藏功能相关属性
  favoriteVoiceIds = [],
  onToggleFavorite,
  showFavoriteButton = false
}: VoiceSelectionListProps) {
  console.log("VoiceSelectionList is rendering"); // 性能优化后，这个只会在相关状态变化时打印

  // 🔧 性能优化：管理图片加载错误状态
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // 🔧 性能优化：使用useMemo缓存样式计算
  const voiceItemStyles = useMemo(() => ({
    base: "relative p-2.5 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50/50 group/item",
    selected: "bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100",
    normal: ""
  }), []);

  const avatarStyles = useMemo(() => ({
    base: "relative w-10 h-10 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12",
    selected: "ring-3 ring-blue-300 scale-110",
    normal: ""
  }), []);

  // 🔧 性能优化：使用useCallback稳定化图片错误处理函数
  const handleImageError = useCallback((voiceId: string) => {
    setImageErrors(prev => new Set(prev).add(voiceId));
  }, []);

  // 🔧 性能优化：使用useCallback稳定化性别样式计算
  const getGenderStyles = useCallback((gender: string) => {
    switch (gender) {
      case "male":
        return {
          badge: "bg-blue-100 text-blue-700 border border-blue-200",
          dot: "bg-blue-500",
          text: "text-blue-600 group-hover/item:text-blue-700",
          fallback: "bg-gradient-to-br from-blue-500 to-blue-700"
        };
      case "female":
        return {
          badge: "bg-pink-100 text-pink-700 border border-pink-200",
          dot: "bg-pink-500",
          text: "text-pink-600 group-hover/item:text-pink-700",
          fallback: "bg-gradient-to-br from-pink-500 to-pink-700"
        };
      default:
        return {
          badge: "bg-gray-100 text-gray-700 border border-gray-200",
          dot: "bg-gray-500",
          text: "text-gray-600 group-hover/item:text-gray-700",
          fallback: "bg-gradient-to-br from-gray-500 to-gray-700"
        };
    }
  }, []);
  return (
    <div className={`${listHeightClass} overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100`}>
      {filteredVoices.length > 0 ? (
        filteredVoices.map((voice) => {
          const genderStyles = getGenderStyles(voice.gender);
          const hasImageError = imageErrors.has(voice.id);

          return (
            <div
              key={voice.id}
              data-voice-id={voice.id}
              className={`${voiceItemStyles.base} ${
                currentVoiceId === voice.id ? voiceItemStyles.selected : voiceItemStyles.normal
              }`}
              onClick={() => onSelectVoice(voice.id)}
            >
            <div className="flex items-center gap-2.5">
              {/* Enhanced Avatar */}
              <div
                className={`${avatarStyles.base} ${
                  currentVoiceId === voice.id ? avatarStyles.selected : avatarStyles.normal
                }`}
              >
                {voiceIconMapping[voice.id] && !hasImageError ? (
                  <img
                    src={voiceIconMapping[voice.id]}
                    alt={voice.name}
                    className="w-full h-full object-cover"
                    onError={() => handleImageError(voice.id)}
                  />
                ) : (
                  // 🔧 性能优化：使用React状态管理而不是直接DOM操作
                  <div className={`w-full h-full rounded-full flex items-center justify-center text-white font-bold ${
                    !voiceIconMapping[voice.id] ? 'animate-pulse' : ''
                  } ${genderStyles.fallback}`}>
                    {voice.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>

              {/* Voice Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <div className="font-semibold text-gray-900 text-base truncate group-hover/item:text-blue-700 transition-colors duration-300">
                    {voice.name}
                  </div>
                  {/* 性别标识 */}
                  <div className={`flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full flex-shrink-0 ${genderStyles.badge}`}>
                    <div className={`w-2 h-2 rounded-full ${genderStyles.dot}`} />
                    <span>
                      {voice.gender === "male" ? "男生" : voice.gender === "female" ? "女生" : "中性"}
                    </span>
                  </div>

                  {/* 选中状态的闪烁动画点 */}
                  {currentVoiceId === voice.id && (
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping flex-shrink-0 ml-10" />
                  )}
                </div>
                <div className={`text-sm leading-relaxed truncate transition-colors duration-300 ${genderStyles.text}`}>
                  {voice.description}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* 【新增】收藏按钮 */}
                {showFavoriteButton && onToggleFavorite && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleFavorite(voice.id);
                    }}
                    className={`button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 group/favorite shadow-lg ${
                      favoriteVoiceIds.includes(voice.id)
                        ? "bg-gradient-to-r from-pink-100 to-red-100 hover:from-pink-200 hover:to-red-200"
                        : "bg-gradient-to-r from-gray-100 to-gray-200 hover:from-pink-100 hover:to-pink-200"
                    }`}
                  >
                    <Heart className={`w-4 h-4 transition-colors duration-300 ${
                      favoriteVoiceIds.includes(voice.id)
                        ? "text-pink-600 fill-current"
                        : "text-gray-600 group-hover/favorite:text-pink-600"
                    }`} />
                  </button>
                )}

                {/* Preview Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleVoicePreview(voice.preview || null, voice.id);
                  }}
                  disabled={!voice.preview}
                  className={`button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 hover:rotate-12 group/play shadow-lg disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 ${
                    previewingVoice === voice.id
                      ? "bg-gradient-to-r from-green-100 to-green-200 hover:from-green-200 hover:to-green-300"
                      : "bg-gradient-to-r from-gray-100 to-gray-200 hover:from-blue-100 hover:to-blue-200"
                  }`}
                >
                  {previewingVoice === voice.id ? (
                    <div className="flex items-center justify-center">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                  ) : (
                    <Play className="w-4 h-4 text-gray-600 group-hover/play:text-blue-600 ml-0.5 transition-colors duration-300" />
                  )}
                </button>
              </div>
            </div>

            {/* Enhanced Selection Indicator */}
            {showSelectionIndicator && currentVoiceId === voice.id && (
              <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-blue-400 via-purple-500 to-pink-400 rounded-r animate-pulse" />
            )}
          </div>
          );
        })
      ) : (
        <div className="p-8 text-center text-gray-500">
          <p>没有找到匹配的声音。</p>
        </div>
      )}
    </div>
  );
}, arePropsEqual);
