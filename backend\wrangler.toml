# wrangler.toml
# 这是根据您提供的 worker.js 生成的 Cloudflare Worker 配置文件。
# 在部署前，请务必将所有 "your_..." 占位符替换为您的实际值。

# 1. 主配置
# --------------------
# name: 您的 Worker 名称，在 Cloudflare 仪表盘上显示。
# main: Worker 的入口文件路径。
# compatibility_date: 兼容性日期，确保 Worker 在一致的环境中运行。
# compatibility_flags: 启用新的 Worker 功能。代码中使用了 ES模块 语法 (export default)，所以需要 "export_default_worker"。
name = "tts-api-proxy"
main = "worker.js"
compatibility_date = "2024-03-01"

# 2. 环境变量 (`vars`)
# --------------------
# 这些变量可以在 Worker 代码中通过 `env.VARIABLE_NAME` 访问。
# 请务必将所有涉密的变量（如密钥）通过 `wrangler secret put` 命令设置，以保证安全。
# 这里为了方便配置，以明文形式列出，但强烈建议使用 secrets。
[vars]
# 调试模式开关 (true / false)
DEBUG = "true"

# JWT 认证配置
JWT_SECRET = "X8k9#mP2$vL5nQ7@jR3wY6*tZ4" # 【重要】请替换为极其复杂的密钥

# 管理系统，卡密生成后台相关变量
ADMIN_USERS = "1060352824"
ELEVENLABS_API_KEY = "OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310"

# R2 直链配置
R2_PATH_PREFIX = "audios"
R2_DIRECT_DOMAIN = "r2-proxy-assets.aispeak.top"


# 腾讯云 SES (邮件服务) 配置
TENCENT_SECRET_ID = "AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9"
TENCENT_SECRET_KEY = "NWUHtIWahkeHl6IMK6f9Zqvl3EF5g8PT"
SES_REGION = "ap-guangzhou" # 默认是广州，可按需修改
FROM_EMAIL = "<EMAIL>" # 【重要】替换为您在SES中验证过的发件地址
FROM_EMAIL_NAME = "tts.aispeak.top" # 发件人名称
VERIFICATION_TEMPLATE_ID = "32699" # 【重要】替换为您的SES邮件模板ID

# TTS 代理配置
PROXY_SECRET = "AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9"
ENABLE_TTS_PROXY = "true" # 是否启用代理功能
TTS_PROXY_URLS = "https://visually-informed-hamster.edgecompute.app,https://ttsproxy-vercel02.aispeak.top,https://ttsproxy-vercel03.aispeak.top,https://kooflfk4ra.execute-api.ap-northeast-3.amazonaws.com/default,https://ttsproxy.aispeak.top,https://bgw8zfag0e.execute-api.ap-south-1.amazonaws.com/default,https://72f99godtj.execute-api.eu-west-3.amazonaws.com,https://4aowvu1vs6.execute-api.us-east-1.amazonaws.com,https://krgl5bu4uf.execute-api.ca-central-1.amazonaws.com,https://whi4og2z6h.execute-api.af-south-1.amazonaws.com,https://vykjc9dtuk.execute-api.me-central-1.amazonaws.com,https://jd3ly39722.execute-api.eu-west-1.amazonaws.com,https://tts-proxy-aqf9cchyfzfaasfp.australiaeast-01.azurewebsites.net,https://tts-proxy-zone-3dl3eogtwifm-1256990318.eo-edgefunctions.com" # 【推荐】新的多代理URL，逗号分隔
TTS_PROXY_SECRET = "AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9" # 访问代理时使用的认证密钥
TTS_PROXY_MODE = "fallback" # 代理模式: 'direct'(直连), 'proxy'(仅代理), 'fallback'(直连失败后代理), 'balanced'(负载均衡)

# 进度消息和DO位置提示配置
ENABLE_PROGRESS_MESSAGES = "false" # 是否向客户端发送详细进度
ENABLE_DO_LOCATION_HINT = "true" # 是否启用Durable Object位置提示以优化延迟

# 自动标注服务配置
AUTO_TAG_API_URL = "https://geminitts.aispeak.top/api/tts/process" # 外部标注API地址
AUTO_TAG_TOKEN = "CM8l3Wqf7TaWah7ruIAxAmMZYcAd274MAeAnFkhvxPg0TMPs" # 外部API访问令牌
AUTO_TAG_TIMEOUT = "30000" # 请求超时时间（毫秒）
AUTO_TAG_RATE_LIMIT = "10" # 每分钟最大请求数

# 3. Durable Object (DO) 绑定
# --------------------
# 代码中定义了 `TtsTaskDomyaitts` 类来处理每个TTS任务。
# `binding` 是在代码中引用的名称 (env.TTS_TASK_DO)。
# `class_name` 是在代码中定义的类名。
[durable_objects]
bindings = [
  { name = "TTS_TASK_DO", class_name = "TtsTaskDomyaitts" }
]

# 【代理版本】Durable Object 迁移记录
# 使用独立的标签避免与原 Worker 冲突
[[migrations]]
  tag = "v1-proxy"
  new_sqlite_classes = ["TtsTaskDomyaitts"]


# 4. KV 命名空间绑定
# --------------------
# 代码使用 KV 存储用户信息、任务状态、卡密和声音映射。
# 在部署前，您需要先在 Cloudflare 创建这些 KV 命名空间。
# `binding` 是代码中引用的名称。
# `id` 是您在 Cloudflare 仪表盘上创建的 KV 命名空间的 ID。
[[kv_namespaces]]
binding = "USERS"
id = "8341ec47189543b48818f57e9ca4e5e0"

[[kv_namespaces]]
binding = "CARDS"
id = "69d6e32b35dd4a0bb996584ebf3f5b27"

[[kv_namespaces]]
binding = "TTS_STATUS"
id = "0ae5fbcb1ed34dab9357ae1a838b34f3"

[[kv_namespaces]]
binding = "VOICE_MAPPINGS"
id = "065bf81a6ad347d19709b402659608f5"


# 5. R2 存储桶绑定
# --------------------
# 代码使用 R2 存储生成的音频文件和预览音频。
# 在部署前，您需要先在 Cloudflare 创建这些 R2 存储桶。
# `binding` 是代码中引用的名称。
# `bucket_name` 是您创建的 R2 存储桶的名称。
[[r2_buckets]]
binding = "AUDIOS"
bucket_name = "audios"

[[r2_buckets]]
binding = "AUDIO_BUCKET"
bucket_name = "preview-audio" # 用于存放预览音频的桶


# 6. Analytics Engine 绑定
# --------------------
# 代码使用 Analytics Engine 记录 DO 的实际运行位置和代理请求的统计数据。
# `binding` 是代码中引用的名称。
# `dataset` 是您在 Cloudflare 中创建的数据集名称。
[[analytics_engine_datasets]]
binding = "DO_ANALYTICS"
dataset = "do_location_stats" # 您可以自定义数据集名称

[[analytics_engine_datasets]]
binding = "PROXY_ANALYTICS"
dataset = "proxy_performance_stats" # 您可以自定义数据集名称


# 开启日志
[observability.logs]
enabled = true

