"use client"

import React, { useRef } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { ChevronDown, Sparkles } from "lucide-react";

interface Model {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

interface ModelSelectorProps {
  selectedModel: string;
  isModelDropdownOpen: boolean;
  availableModels: Model[];
  onModelChange: (modelId: string) => void;
  onToggleDropdown: () => void;
  modelDropdownRef: React.RefObject<HTMLDivElement | null>;
}

// 🔧 性能优化：自定义比较函数，避免因为React元素引用变化导致的重渲染
const arePropsEqual = (prevProps: ModelSelectorProps, nextProps: ModelSelectorProps) => {
  // 比较基本属性
  if (
    prevProps.selectedModel !== nextProps.selectedModel ||
    prevProps.isModelDropdownOpen !== nextProps.isModelDropdownOpen ||
    prevProps.onModelChange !== nextProps.onModelChange ||
    prevProps.onToggleDropdown !== nextProps.onToggleDropdown ||
    prevProps.modelDropdownRef !== nextProps.modelDropdownRef
  ) {
    return false;
  }

  // 比较availableModels数组的长度和ID
  if (prevProps.availableModels.length !== nextProps.availableModels.length) {
    return false;
  }

  // 只比较模型的ID，忽略React元素的引用变化
  for (let i = 0; i < prevProps.availableModels.length; i++) {
    if (prevProps.availableModels[i].id !== nextProps.availableModels[i].id) {
      return false;
    }
  }

  return true;
};

// 使用 React.memo 包裹模型选择组件 - 防止父组件重渲染时重新渲染
export const ModelSelector = React.memo(function ModelSelector({
  selectedModel,
  isModelDropdownOpen,
  availableModels,
  onModelChange,
  onToggleDropdown,
  modelDropdownRef
}: ModelSelectorProps) {
  console.log("ModelSelector is rendering"); // 性能优化后，这个只会在模型相关状态变化时打印

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
  };

  return (
    <Card className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-50 rounded-2xl">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <CardContent className="p-6 relative">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md opacity-50" />
              <div className="relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <h3 className="text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              模型
            </h3>
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
              <span className="relative px-3 py-1.5 text-sm font-semibold bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 rounded-lg text-purple-700 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 cursor-default flex items-center gap-1">
                <Sparkles className="w-3 h-3" />
                只有Eleven v3才支持情感标注词
              </span>
            </div>
          </div>
        </div>

        <div className="relative" ref={modelDropdownRef}>
          <button
            onClick={onToggleDropdown}
            className={`w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ${
              isModelDropdownOpen
                ? "border-indigo-400 shadow-2xl shadow-indigo-100/50 ring-4 ring-indigo-50 scale-[1.02]"
                : "border-gray-200 hover:border-gray-300 hover:shadow-lg"
            }`}
          >
            <div className="flex items-center gap-3">
              {selectedModel ? (
                <>
                  <div className="text-lg flex items-center justify-center">
                    {availableModels.find((m) => m.id === selectedModel)?.icon}
                  </div>
                  <div>
                    <div className={`font-semibold text-base ${
                      selectedModel === "eleven_v3"
                        ? "bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold"
                        : "text-gray-900"
                    }`}>
                      {availableModels.find((m) => m.id === selectedModel)?.name}
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-gray-500 text-base">请选择模型...</div>
              )}
            </div>
            <ChevronDown
              className={`w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-indigo-500 ${
                isModelDropdownOpen ? "rotate-180 text-indigo-500" : ""
              }`}
            />
          </button>

          {isModelDropdownOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-[99999] overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100">
              <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {availableModels.map((model) => (
                  <div
                    key={model.id}
                    className={`relative p-3 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-indigo-50/50 group/item ${
                      selectedModel === model.id
                        ? "bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-100"
                        : ""
                    }`}
                    onClick={() => handleModelSelect(model.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl transition-all duration-300 group-hover/item:scale-110 flex items-center justify-center">
                        {model.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-0.5">
                          <span className={`font-semibold text-base ${
                            model.id === "eleven_v3"
                              ? "bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold"
                              : "text-gray-900"
                          }`}>
                            {model.name}
                          </span>
                          {selectedModel === model.id && (
                            <div className="w-3 h-3 bg-indigo-500 rounded-full animate-ping flex-shrink-0" />
                          )}
                        </div>
                        <div className="text-sm text-gray-600 leading-relaxed">
                          {model.description}
                        </div>
                      </div>
                    </div>

                    {selectedModel === model.id && (
                      <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-400 rounded-r animate-pulse" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}, arePropsEqual);
