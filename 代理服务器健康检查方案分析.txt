请求方法统一为GET
判断代理服务器的URL后缀以aispeak.top结尾的使用/api/health请求健康接口
响应1：
{
    "status": "degraded",
    "healthScore": 70,
    "message": "Enhanced TTS Proxy Server with Robust Error Handling",
    "timestamp": "2025-07-29T07:13:42.498Z",
    "uptime": {
        "seconds": 592268,
        "human": "6d 20h 31m 8s"
    },
    "system": {
        "platform": "ubuntu-express",
        "version": "3.0.0-robust",
        "nodeVersion": "v18.20.8",
        "pid": 936675
    },
    "memory": {
        "used": 21,
        "total": 25,
        "external": 2,
        "rss": 54,
        "unit": "MB",
        "usage": 83
    },
    "features": {
        "robustErrorHandling": true,
        "concurrencyControl": true,
        "circuitBreaker": true,
        "browserFingerprinting": true,
        "realTimeStreaming": true,
        "memoryOptimized": true
    },
    "concurrency": {
        "status": "active",
        "enabled": false,
        "configuration": {
            "maxConcurrentPerVoice": 3,
            "concurrencyEnabled": false
        },
        "statistics": {
            "totalRequests": 131,
            "activeRequests": 5,
            "rejectedRequests": 0,
            "activeByVoice": {
                "T2OtqAWGDq4sh6HX3MwH": 4,
                "tapn1QwocNXk3viVSowa": 1
            },
            "totalActiveVoices": 2
        },
        "activeRequests": {
            "T2OtqAWGDq4sh6HX3MwH": [
                "req_1753404076337_37",
                "req_1753493143720_75",
                "req_1753512985871_96",
                "req_1753522085026_100"
            ],
            "tapn1QwocNXk3viVSowa": [
                "req_1753428264959_64"
            ]
        }
    },
    "circuitBreaker": {
        "status": "CLOSED",
        "isHealthy": true,
        "configuration": {
            "failureThreshold": 800,
            "resetTimeout": 10000,
            "monitoringPeriod": 10000
        },
        "statistics": {
            "totalRequests": 131,
            "totalFailures": 36,
            "totalSuccesses": 95,
            "circuitOpenCount": 0,
            "lastStateChange": 1753180954317
        },
        "currentFailures": 3,
        "lastFailure": 1753761933591
    },
    "fingerprinting": {
        "activeSessions": 0,
        "availableProfiles": 5,
        "profileNames": [
            "Chrome 125 on Windows 11",
            "Chrome 124 on Windows 10",
            "Firefox 126 on Windows 11",
            "Safari 17 on macOS Sonoma",
            "Microsoft Edge 125 on Windows 11"
        ]
    }
}

响应2：
{
    "status": "healthy",
    "message": "Proxy server is up and running on Vercel",
    "timestamp": "2025-07-29T07:17:20.328Z",
    "platform": "vercel"
}

其他代理URL使用/api/v1/health请求健康接口 示例：https://72f99godtj.execute-api.eu-west-3.amazonaws.com/api/v1/health
响应：
{
    "status": "healthy",
    "message": "Proxy server is up and running on AWS Lambda",
    "timestamp": "2025-07-29T07:18:44.163Z",
    "platform": "aws-lambda",
    "region": "eu-west-3",
    "functionName": "tts-proxy-ttsproxy-1HxGK1tDBGIk",
    "requestId": "bf479b01-36ec-4c2e-aa7c-cdc0274a26ff"
}

代理服务有3中响应格式，其中以aispeak.top后缀结尾的有2中响应，剩下其他的1中，共3种，需要实现能正确识别这3中格式。



代理URL：
https://tts-proxy-hk-1.aispeak.top
https://tts-proxy-hk-2.aispeak.top
https://tts-proxy-hk-3.aispeak.top
https://h6raam5ii1.execute-api.sa-east-1.amazonaws.com
https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app
https://cloudrun-tts-proxy-747917692143.europe-west4.run.app
https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
https://cloudrun-tts-proxy-747917692143.us-west4.run.app
https://m335qyfgcd.execute-api.il-central-1.amazonaws.com
https://2q9k4hf821.execute-api.sa-east-1.amazonaws.com
https://cfq8zznahi.execute-api.ap-southeast-3.amazonaws.com
https://visually-informed-hamster.edgecompute.app
https://ttsproxy-vercel02.aispeak.top
https://ttsproxy-vercel03.aispeak.top
https://ttsproxy.aispeak.top
https://72f99godtj.execute-api.eu-west-3.amazonaws.com
https://4aowvu1vs6.execute-api.us-east-1.amazonaws.com
https://krgl5bu4uf.execute-api.ca-central-1.amazonaws.com
https://whi4og2z6h.execute-api.af-south-1.amazonaws.com
https://vykjc9dtuk.execute-api.me-central-1.amazonaws.com
https://jd3ly39722.execute-api.eu-west-1.amazonaws.com
https://tts-proxy-aqf9cchyfzfaasfp.australiaeast-01.azurewebsites.net
https://k7wydmwft6.execute-api.us-west-2.amazonaws.com
https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com