
2025-07-29 09:00:16:926
UTC
[INFO] [2025-07-29T09:00:16.926Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Dialogue task finished, scheduling cleanup
2025-07-29 09:00:16:926
UTC
[INFO] [2025-07-29T09:00:16.926Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Closing all WebSocket sessions
2025-07-29 09:00:16:926
UTC
[INFO] [2025-07-29T09:00:16.926Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Dialogue TTS task completed successfully on attempt 1
2025-07-29 09:00:16:926
UTC
[KV-STATUS] ✅ Successfully stored status for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3
2025-07-29 09:00:16:778
UTC
[KV-STATUS] Storing status for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3:
2025-07-29 09:00:16:778
UTC
[USAGE-UPDATE] B Backend API update successful for user ********
2025-07-29 09:00:16:242
UTC
[USAGE-UPDATE] Using B Backend API for user ********, chars: 465
2025-07-29 09:00:16:242
UTC
[INFO] [2025-07-29T09:00:16.242Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Dialogue TTS task core execution completed successfully
2025-07-29 09:00:16:242
UTC
[R2-AUDIO] ✅ Successfully stored audio for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3:
2025-07-29 09:00:13:331
UTC
[R2-AUDIO] Storing audio for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3:
2025-07-29 09:00:13:331
UTC
[INFO] [2025-07-29T09:00:13.331Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Audio reordering completed
2025-07-29 09:00:13:331
UTC
[INFO] [2025-07-29T09:00:13.331Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - completed
2025-07-29 09:00:13:331
UTC
[INFO] [2025-07-29T09:00:13.331Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 09:00:13:331
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 09:00:13:331
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 09:00:13:331
UTC
[ELEVENLABS-API] ✅ Request successful:
2025-07-29 08:59:57:170
UTC
[INFO] [2025-07-29T08:59:57.170Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:59:57:170
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:59:57:170
UTC
[INFO] [2025-07-29T08:59:57.170Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:59:57:170
UTC
[INFO] [2025-07-29T08:59:57.170Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:59:57:170
UTC
[INFO] [2025-07-29T08:59:57.170Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:59:57:170
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:59:57:170
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:59:57:170
UTC
[ELEVENLABS-API] ✅ Request successful:
2025-07-29 08:59:23:207
UTC
[WARN] [2025-07-29T08:59:23.207Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4483ms...
2025-07-29 08:59:23:207
UTC
[WARN] [2025-07-29T08:59:23.207Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:50:878
UTC
[WARN] [2025-07-29T08:58:50.878Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2329ms...
2025-07-29 08:58:50:878
UTC
[WARN] [2025-07-29T08:58:50.878Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:50:878
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:50:878
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:50:726
UTC
[INFO] [2025-07-29T08:58:50.726Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:50:726
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:50:726
UTC
[INFO] [2025-07-29T08:58:50.726Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:50:726
UTC
[INFO] [2025-07-29T08:58:50.726Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:50:726
UTC
[INFO] [2025-07-29T08:58:50.726Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:50:726
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:50:726
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:50:726
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:50:726
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:50:726
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:50:556
UTC
[PROXY-SUCCESS] ✅ Proxy #2 (https://cloudrun-tts-proxy-747917692143.asia-east1.run.app) successful on cluster attempt #1!
2025-07-29 08:58:38:127
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #2 (health check enabled)
2025-07-29 08:58:38:127
UTC
[QUICK-HEALTH] ✅ Proxy #2 passed quick health check (387ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:38:127
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:37:740
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app (3000ms timeout)
2025-07-29 08:58:37:740
UTC
[INFO] [2025-07-29T08:58:37.740Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:37:740
UTC
[PROXY-STATS] ❌ Recorded proxy failure
2025-07-29 08:58:37:740
UTC
[ANALYTICS-PROXY] 📊 Recorded failure event:
2025-07-29 08:58:37:286
UTC
[ERROR] [2025-07-29T08:58:37.286Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Proxy #1 failed with status 401
2025-07-29 08:58:36:900
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:36:900
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (1905ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app
2025-07-29 08:58:36:900
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app
2025-07-29 08:58:34:995
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app (3000ms timeout)
2025-07-29 08:58:34:995
UTC
[INFO] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:34:995
UTC
[INFO] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:34:995
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:34:995
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:34:995
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:34:995
UTC
[WARN] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:34:995
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:34:995
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:34:782
UTC
[INFO] [2025-07-29T08:58:34.782Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - completed
2025-07-29 08:58:34:782
UTC
[INFO] [2025-07-29T08:58:34.782Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:34:782
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:34:782
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:34:782
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:34:782
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:34:782
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:34:619
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app) successful on cluster attempt #1!
2025-07-29 08:58:32:321
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:32:321
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (2119ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app
2025-07-29 08:58:32:321
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app
2025-07-29 08:58:30:778
UTC
[WARN] [2025-07-29T08:58:30.778Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4079ms...
2025-07-29 08:58:30:778
UTC
[WARN] [2025-07-29T08:58:30.778Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:30:778
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:30:778
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:30:202
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app (3000ms timeout)
2025-07-29 08:58:30:202
UTC
[INFO] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:30:202
UTC
[INFO] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:30:202
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:30:202
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:30:202
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:30:202
UTC
[WARN] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:30:202
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:30:202
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:28:138
UTC
[WARN] [2025-07-29T08:58:28.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2490ms...
2025-07-29 08:58:28:138
UTC
[WARN] [2025-07-29T08:58:28.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:28:138
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:28:138
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:27:989
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:27:989
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:27:989
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:27:989
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:27:989
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:27:989
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:27:834
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://cloudrun-tts-proxy-747917692143.asia-east1.run.app) successful on cluster attempt #1!
2025-07-29 08:58:25:867
UTC
[WARN] [2025-07-29T08:58:25.867Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4186ms...
2025-07-29 08:58:25:867
UTC
[WARN] [2025-07-29T08:58:25.867Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:25:867
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:25:867
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:23:423
UTC
[WARN] [2025-07-29T08:58:23.423Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2310ms...
2025-07-29 08:58:23:423
UTC
[WARN] [2025-07-29T08:58:23.423Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:23:423
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:23:423
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:23:277
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:36:900
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (1905ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app
2025-07-29 08:58:36:900
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app
2025-07-29 08:58:34:995
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.europe-west4.run.app (3000ms timeout)
2025-07-29 08:58:34:995
UTC
[INFO] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:34:995
UTC
[INFO] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:34:995
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:34:995
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:34:995
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:34:995
UTC
[WARN] [2025-07-29T08:58:34.995Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:34:995
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:34:995
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:34:782
UTC
[INFO] [2025-07-29T08:58:34.782Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - completed
2025-07-29 08:58:34:782
UTC
[INFO] [2025-07-29T08:58:34.782Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:34:782
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:34:782
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:34:782
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:34:782
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:34:782
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:34:619
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app) successful on cluster attempt #1!
2025-07-29 08:58:32:321
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:32:321
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (2119ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app
2025-07-29 08:58:32:321
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app
2025-07-29 08:58:30:778
UTC
[WARN] [2025-07-29T08:58:30.778Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4079ms...
2025-07-29 08:58:30:778
UTC
[WARN] [2025-07-29T08:58:30.778Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:30:778
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:30:778
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:30:202
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app (3000ms timeout)
2025-07-29 08:58:30:202
UTC
[INFO] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:30:202
UTC
[INFO] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:30:202
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:30:202
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:30:202
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:30:202
UTC
[WARN] [2025-07-29T08:58:30.202Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:30:202
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:30:202
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:28:138
UTC
[WARN] [2025-07-29T08:58:28.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2490ms...
2025-07-29 08:58:28:138
UTC
[WARN] [2025-07-29T08:58:28.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:28:138
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:28:138
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:27:989
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:27:989
UTC
[INFO] [2025-07-29T08:58:27.989Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:27:989
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:27:989
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:27:989
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:27:989
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:27:989
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:27:834
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://cloudrun-tts-proxy-747917692143.asia-east1.run.app) successful on cluster attempt #1!
2025-07-29 08:58:25:867
UTC
[WARN] [2025-07-29T08:58:25.867Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4186ms...
2025-07-29 08:58:25:867
UTC
[WARN] [2025-07-29T08:58:25.867Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:25:867
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:25:867
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:23:423
UTC
[WARN] [2025-07-29T08:58:23.423Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2310ms...
2025-07-29 08:58:23:423
UTC
[WARN] [2025-07-29T08:58:23.423Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:23:423
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:23:423
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:23:277
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:23:277
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:23:277
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:23:277
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:23:277
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:23:277
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:22:721
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com) successful on cluster attempt #1!
2025-07-29 08:58:20:816
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:20:816
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (2080ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:20:816
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:19:955
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:19:955
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (1073ms), proceeding with request: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com
2025-07-29 08:58:19:955
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com
2025-07-29 08:58:18:882
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com (3000ms timeout)
2025-07-29 08:58:18:882
UTC
[INFO] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:18:882
UTC
[INFO] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:18:882
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:18:882
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:18:882
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:18:882
UTC
[WARN] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:18:882
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:18:882
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:18:736
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app (3000ms timeout)
2025-07-29 08:58:18:736
UTC
[INFO] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:18:736
UTC
[INFO] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:18:736
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:18:736
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:18:736
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:18:736
UTC
[WARN] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:18:736
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:18:736
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:14:637
UTC
[WARN] [2025-07-29T08:58:14.637Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4004ms...
2025-07-29 08:58:14:637
UTC
[WARN] [2025-07-29T08:58:14.637Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:14:637
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:14:637
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:14:552
UTC
[WARN] [2025-07-29T08:58:14.552Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4022ms...
2025-07-29 08:58:14:552
UTC
[WARN] [2025-07-29T08:58:14.552Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:14:552
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:14:552
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:12:138
UTC
[WARN] [2025-07-29T08:58:12.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2317ms...
2025-07-29 08:58:12:138
UTC
[WARN] [2025-07-29T08:58:12.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:12:138
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:12:138
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:12:137
UTC
[WARN] [2025-07-29T08:58:12.137Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2268ms...
2025-07-29 08:58:12:137
UTC
[WARN] [2025-07-29T08:58:12.137Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:12:137
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:12:137
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:11:968
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - started
2025-07-29 08:58:11:968
UTC
[CACHE] Updated memory cache for voiceIdMapping. Next refresh in 300s.
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:11:967
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - started
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:23:277
UTC
[INFO] [2025-07-29T08:58:23.277Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - completed
2025-07-29 08:58:23:277
UTC
Successfully processed 1/1 chunks concurrently
2025-07-29 08:58:23:277
UTC
Processing completed: 1 successful, 0 failed
2025-07-29 08:58:23:277
UTC
[PROXY-SUCCESS] ✅ Proxy failover successful, returning audio data
2025-07-29 08:58:23:277
UTC
[PROXY-STATS] ✅ Recorded proxy success
2025-07-29 08:58:23:277
UTC
[ANALYTICS-PROXY] 📊 Recorded success event:
2025-07-29 08:58:22:721
UTC
[PROXY-SUCCESS] ✅ Proxy #1 (https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com) successful on cluster attempt #1!
2025-07-29 08:58:20:816
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:20:816
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (2080ms), proceeding with request: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:20:816
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app
2025-07-29 08:58:19:955
UTC
[PROXY-TIMEOUT] Using 60s timeout for proxy #1 (health check enabled)
2025-07-29 08:58:19:955
UTC
[QUICK-HEALTH] ✅ Proxy #1 passed quick health check (1073ms), proceeding with request: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com
2025-07-29 08:58:19:955
UTC
[QUICK-HEALTH] ✅ Proxy quick check passed: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com
2025-07-29 08:58:18:882
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://vs30t3pkma.execute-api.eu-west-2.amazonaws.com (3000ms timeout)
2025-07-29 08:58:18:882
UTC
[INFO] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:18:882
UTC
[INFO] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:18:882
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:18:882
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:18:882
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:18:882
UTC
[WARN] [2025-07-29T08:58:18.882Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:18:882
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:18:882
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:18:736
UTC
[QUICK-HEALTH] 🚀 Quick health check for proxy: https://cloudrun-tts-proxy-747917692143.asia-east1.run.app (3000ms timeout)
2025-07-29 08:58:18:736
UTC
[INFO] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-attempt - starting
2025-07-29 08:58:18:736
UTC
[INFO] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] [op:flow] - Flow: proxy-failover-start - initiated
2025-07-29 08:58:18:736
UTC
[PROXY-STRATEGY] Random selection strategy enabled. Shuffled healthy proxy order:
2025-07-29 08:58:18:736
UTC
[HEALTH-CHECK] 🚀 Using real-time health verification for 24 proxies (no pre-screening)
2025-07-29 08:58:18:736
UTC
[PROXY-TRIGGER] Direct ElevenLabs calls failed with retryable error. Triggering proxy failover...
2025-07-29 08:58:18:736
UTC
[WARN] [2025-07-29T08:58:18.736Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, giving up
2025-07-29 08:58:18:736
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:18:736
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:14:637
UTC
[WARN] [2025-07-29T08:58:14.637Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4004ms...
2025-07-29 08:58:14:637
UTC
[WARN] [2025-07-29T08:58:14.637Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:14:637
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:14:637
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:14:552
UTC
[WARN] [2025-07-29T08:58:14.552Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 4022ms...
2025-07-29 08:58:14:552
UTC
[WARN] [2025-07-29T08:58:14.552Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:14:552
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:14:552
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:12:138
UTC
[WARN] [2025-07-29T08:58:12.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2317ms...
2025-07-29 08:58:12:138
UTC
[WARN] [2025-07-29T08:58:12.138Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:12:138
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:12:138
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:12:137
UTC
[WARN] [2025-07-29T08:58:12.137Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - [ELEVENLABS-RETRY] Request failed. Retrying in 2268ms...
2025-07-29 08:58:12:137
UTC
[WARN] [2025-07-29T08:58:12.137Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Request error, retrying
2025-07-29 08:58:12:137
UTC
[ELEVENLABS-API] ❌ Request failed:
2025-07-29 08:58:12:137
UTC
[ELEVENLABS-API] ❌ Failed to parse error response as JSON. Raw response: <!doctype html><meta charset="utf-8"><meta name=viewport content="width=device-width, initial-scale=1"><title>429</title>429 Too Many Requests
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:11:968
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:11:968
UTC
[INFO] [2025-07-29T08:58:11.968Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - started
2025-07-29 08:58:11:968
UTC
[CACHE] Updated memory cache for voiceIdMapping. Next refresh in 300s.
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [chunk:1/1] - TTS Request Details (fallback mode)
2025-07-29 08:58:11:967
UTC
[CONCURRENCY] Calculated optimal concurrency for 1 chunks: 1 (Strategy: Simple Max)
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Processing 1 chunks with dynamic concurrency...
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: speaker-in-voice-group - started
2025-07-29 08:58:11:967
UTC
[INFO] [2025-07-29T08:58:11.967Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] [op:flow] - Flow: voice-group-processing - started
2025-07-29 08:58:11:967
UTC
[CACHE] Updated memory cache for voiceIdMapping. Next refresh in 300s.
2025-07-29 08:58:11:906
UTC
[KV-FETCH] Fetching voiceIdMapping from KV store.
2025-07-29 08:58:11:906
UTC
[KV-FETCH] Fetching voiceIdMapping from KV store.
2025-07-29 08:58:11:906
UTC
[INFO] [2025-07-29T08:58:11.906Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Voice grouping completed
2025-07-29 08:58:11:906
UTC
[QUOTA-CHECK] B Backend API check passed for user ********
2025-07-29 08:58:11:296
UTC
[QUOTA-CHECK] Using B Backend API for user ********, tier: PRO, chars: 465
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Dialogue TTS task core execution starting
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Starting Dialogue TTS task attempt 1/3
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Task starting
2025-07-29 08:58:10:835
UTC
[INFO] [2025-07-29T08:58:10.835Z] [user:unknown] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - WebSocket session initialized, sent taskId to client
2025-07-29 08:58:10:835
UTC
[ANALYTICS] Recorded actual location for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3, colo: KIX
2025-07-29 08:58:10:665
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/tts/ws/dialogue/generate
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] Dialogue TTS task 595a38ce-50d2-4c6e-9ed8-c63dd9818acc assigned to location: enam
2025-07-29 08:58:10:528
UTC
[ANALYTICS] Recorded creation intent for dialogue TTS task 595a38ce-50d2-4c6e-9ed8-c63dd9818acc, locationHint: enam
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] ✅ Success! Selected hint 'enam' from Priority Tier 1 (Highest (APAC)).
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] Starting priority-based location selection. Excluding: []
2025-07-29 08:58:10:519
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/tts/ws/dialogue/generate
2025-07-29 08:57:42:997
UTC
[RESPONSE-CONVERT] Output flat structure:
2025-07-29 08:57:42:997
UTC
[RESPONSE-CONVERT] Input structure:
2025-07-29 08:57:42:997
UTC
[RESPONSE-CONVERT] Converting nested response to flat structure for frontend
2025-07-29 08:57:42:997
UTC
[QUOTA-TRANSFORM] Transformed response structure:
2025-07-29 08:57:42:997
UTC
[QUOTA-TRANSFORM] Extracted username from token: ********
2025-07-29 08:57:42:997
UTC
[QUOTA-TRANSFORM] Original response keys:
2025-07-29 08:57:42:997
UTC
[QUOTA-TRANSFORM] Converting flat response to nested format (Main backend format)
2025-07-29 08:57:42:953
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/user/quota
2025-07-29 08:57:42:860
UTC
OPTIONS https://myaitts-worker.panxuchao19951206.workers.dev/api/user/quota
2025-07-29 08:57:41:858
UTC
POST https://myaitts-worker.panxuchao19951206.workers.dev/api/auth/login
2025-07-29 08:57:41:392
UTC
OPTIONS https://myaitts-worker.panxuchao19951206.workers.dev/api/auth/login
2025-07-29 08:57:24:833
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/user/quota
2025-07-29 08:57:24:723
UTC
OPTIONS https://myaitts-worker.panxuchao19951206.workers.dev/api/user/quota
2025-07-29 08:34:59:634
UTC
[DO-ALARM] State for task bd4260b412130fd1d223d9bf53960277c4c9913643e323aab0f8a73d62994361 has been deleted.
2025-07-29 08:34:59:634
UTC
[DO-ALARM] Closed any lingering WebSocket sessions.
2025-07-29 08:34:59:634
UTC
[DO-ALARM] Triggered for task: bd4260b412130fd1d223d9bf53960277c4c9913643e323aab0f8a73d62994361.
2025-07-29 08:34:59:333
UTC
2025-07-29T08:34:59.285Z
2025-07-29 08:58:11:967
UTC
[CACHE] Updated memory cache for voiceIdMapping. Next refresh in 300s.
2025-07-29 08:58:11:906
UTC
[KV-FETCH] Fetching voiceIdMapping from KV store.
2025-07-29 08:58:11:906
UTC
[KV-FETCH] Fetching voiceIdMapping from KV store.
2025-07-29 08:58:11:906
UTC
[INFO] [2025-07-29T08:58:11.906Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Voice grouping completed
2025-07-29 08:58:11:906
UTC
[QUOTA-CHECK] B Backend API check passed for user ********
2025-07-29 08:58:11:296
UTC
[QUOTA-CHECK] Using B Backend API for user ********, tier: PRO, chars: 465
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Dialogue TTS task core execution starting
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Starting Dialogue TTS task attempt 1/3
2025-07-29 08:58:11:296
UTC
[INFO] [2025-07-29T08:58:11.296Z] [user:********] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - Task starting
2025-07-29 08:58:10:835
UTC
[INFO] [2025-07-29T08:58:10.835Z] [user:unknown] [task:41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3] - WebSocket session initialized, sent taskId to client
2025-07-29 08:58:10:835
UTC
[ANALYTICS] Recorded actual location for task 41ce2ea7bc9f39cf7aa21ff4f554b347c6e4b9869cf3e1968c5ee5a3f248eef3, colo: KIX
2025-07-29 08:58:10:665
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/tts/ws/dialogue/generate
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] Dialogue TTS task 595a38ce-50d2-4c6e-9ed8-c63dd9818acc assigned to location: enam
2025-07-29 08:58:10:528
UTC
[ANALYTICS] Recorded creation intent for dialogue TTS task 595a38ce-50d2-4c6e-9ed8-c63dd9818acc, locationHint: enam
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] ✅ Success! Selected hint 'enam' from Priority Tier 1 (Highest (APAC)).
2025-07-29 08:58:10:528
UTC
[DO-ROUTING] Starting priority-based location selection. Excluding: []
2025-07-29 08:58:10:519
UTC
GET https://myaitts-worker.panxuchao19951206.workers.dev/api/tts/ws/dialogue/generate