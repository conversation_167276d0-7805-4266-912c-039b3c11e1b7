/**
 * 实时健康验证功能测试脚本
 * 测试新的快速健康检查机制，验证3秒快速检查和故障转移逻辑
 */

const fs = require('fs');

// 模拟代理配置
const proxyConfig = {
  TTS_HEALTH_CHECK_ENABLED: true,
  TTS_HEALTH_CHECK_TIMEOUT: 2000,
  TTS_HEALTH_CHECK_RETRIES: 2,
  TTS_HEALTH_CHECK_INTERVAL: 2000,
  TTS_HEALTHY_PROXY_TIMEOUT: 60000,
  TTS_PROXY_TIMEOUT: 45000,
  TTS_PROXY_SECRET: 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  ENABLE_PROXY_DEBUG: true,
  TTS_PROXY_URLS: [
    'https://tts-proxy-hk-1.aispeak.top',
    'https://tts-proxy-hk-2.aispeak.top',
    'https://tts-proxy-hk-3.aispeak.top',
    'https://h6raam5ii1.execute-api.sa-east-1.amazonaws.com', // 这个应该404
    'https://cloudrun-tts-proxy-747917692143.asia-northeast1.run.app',
    'https://ttsproxy-vercel02.aispeak.top'
  ]
};

const mockEnv = {};

// 从worker.js复制的函数
function getProxyHealthUrl(proxyUrl) {
  if (proxyUrl.includes('amazonaws.com')) {
    return proxyUrl + '/api/v1/health';  // AWS Lambda代理
  } else {
    return proxyUrl + '/api/health';     // 其他所有代理
  }
}

async function quickHealthCheck(proxyUrl, proxyConfig, env, timeoutMs = 3000) {
  const healthUrl = getProxyHealthUrl(proxyUrl);
  
  try {
    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      console.log(`[QUICK-HEALTH] 🚀 Quick health check for proxy: ${proxyUrl} (${timeoutMs}ms timeout)`);
    }

    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'x-proxy-secret': proxyConfig.TTS_PROXY_SECRET
      },
      signal: AbortSignal.timeout(timeoutMs)
    });

    const isHealthy = response.status === 200;
    
    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      if (isHealthy) {
        console.log(`[QUICK-HEALTH] ✅ Proxy quick check passed: ${proxyUrl}`);
      } else {
        console.warn(`[QUICK-HEALTH] ⚠️ Proxy quick check failed (status ${response.status}): ${proxyUrl}`);
      }
    }
    
    return isHealthy;
  } catch (error) {
    if (proxyConfig.ENABLE_PROXY_DEBUG) {
      console.warn(`[QUICK-HEALTH] ❌ Proxy quick check error: ${proxyUrl} - ${error.message}`);
    }
    return false;
  }
}

// 模拟代理故障转移逻辑（简化版）
async function simulateProxyFailover(proxyUrls, proxyConfig, env) {
  console.log(`[FAILOVER-SIM] 🚀 Starting proxy failover simulation with ${proxyUrls.length} proxies`);
  
  const startTime = Date.now();
  let totalQuickChecks = 0;
  let skippedProxies = 0;
  let successfulProxy = null;
  
  for (let i = 0; i < proxyUrls.length; i++) {
    const proxyUrl = proxyUrls[i];
    console.log(`\n[FAILOVER-SIM] 🔄 Attempting proxy #${i + 1}/${proxyUrls.length}: ${proxyUrl}`);
    
    // 实时健康验证
    if (proxyConfig.TTS_HEALTH_CHECK_ENABLED) {
      const quickCheckStart = Date.now();
      totalQuickChecks++;
      
      const isStillHealthy = await quickHealthCheck(proxyUrl, proxyConfig, env, proxyConfig.TTS_HEALTH_CHECK_TIMEOUT);
      const quickCheckDuration = Date.now() - quickCheckStart;
      
      if (!isStillHealthy) {
        console.warn(`[FAILOVER-SIM] ❌ Proxy #${i + 1} failed quick health check (${quickCheckDuration}ms), skipping to next proxy`);
        skippedProxies++;
        
        if (i === proxyUrls.length - 1) {
          console.error(`[FAILOVER-SIM] 💀 All proxies failed quick health check`);
          break;
        }
        continue; // 跳到下一个代理
      }
      
      console.log(`[FAILOVER-SIM] ✅ Proxy #${i + 1} passed quick health check (${quickCheckDuration}ms), would proceed with actual request`);
    }
    
    // 模拟实际请求成功
    console.log(`[FAILOVER-SIM] 🎯 Proxy #${i + 1} would be used for actual TTS request (60s timeout)`);
    successfulProxy = proxyUrl;
    break;
  }
  
  const totalTime = Date.now() - startTime;
  
  return {
    totalTime,
    totalQuickChecks,
    skippedProxies,
    successfulProxy,
    totalProxies: proxyUrls.length
  };
}

// 主测试函数
async function testRealTimeHealthVerification() {
  console.log('🚀 开始实时健康验证功能测试...\n');
  
  console.log('📋 配置信息:');
  console.log(`- 实时健康检查启用: ${proxyConfig.TTS_HEALTH_CHECK_ENABLED}`);
  console.log(`- 快速检查超时: ${proxyConfig.TTS_HEALTH_CHECK_TIMEOUT}ms`);
  console.log(`- 健康代理超时: ${proxyConfig.TTS_HEALTHY_PROXY_TIMEOUT}ms`);
  console.log(`- 代理数量: ${proxyConfig.TTS_PROXY_URLS.length}`);
  console.log('');
  
  console.log('🔍 测试快速健康检查URL生成:');
  proxyConfig.TTS_PROXY_URLS.forEach((url, index) => {
    const healthUrl = getProxyHealthUrl(url);
    console.log(`${index + 1}. ${url} -> ${healthUrl}`);
  });
  console.log('');
  
  console.log('🏥 开始实时健康验证测试...\n');
  
  const result = await simulateProxyFailover(proxyConfig.TTS_PROXY_URLS, proxyConfig, mockEnv);
  
  console.log('\n📊 测试结果汇总:');
  console.log(`- 总代理数: ${result.totalProxies}`);
  console.log(`- 快速检查次数: ${result.totalQuickChecks}`);
  console.log(`- 跳过的代理数: ${result.skippedProxies}`);
  console.log(`- 成功的代理: ${result.successfulProxy || '无'}`);
  console.log(`- 总耗时: ${result.totalTime}ms`);
  console.log(`- 平均每次快速检查: ${result.totalQuickChecks > 0 ? (result.totalTime / result.totalQuickChecks).toFixed(0) : 0}ms`);
  
  console.log('\n🎯 性能对比:');
  console.log('- 传统方案 (60秒×失效代理数): 可能需要数分钟');
  console.log(`- 实时验证方案 (实际耗时): ${result.totalTime}ms`);
  console.log(`- 跳过了 ${result.skippedProxies} 个不健康代理，避免了 ${result.skippedProxies * 60} 秒等待`);
  
  if (result.successfulProxy) {
    console.log('\n✅ 实时健康验证功能测试完成! 成功找到可用代理');
  } else {
    console.log('\n❌ 所有代理都不可用，但快速检查避免了长时间等待');
  }
}

// 运行测试
if (require.main === module) {
  testRealTimeHealthVerification().catch(console.error);
}

module.exports = {
  testRealTimeHealthVerification,
  quickHealthCheck,
  simulateProxyFailover,
  getProxyHealthUrl
};
