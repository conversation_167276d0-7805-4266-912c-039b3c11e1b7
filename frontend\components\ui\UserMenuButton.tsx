"use client"

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { User, Key, LogOut } from "lucide-react";

interface UserMenuButtonProps {
  userEmail: string;
  showUserMenu: boolean;
  onToggleMenu: () => void;
  onChangePassword: () => void;
  onLogout: () => void;
  userMenuRef: React.RefObject<HTMLDivElement | null>;
}

// 使用 React.memo 包裹用户菜单按钮组件 - 防止父组件重渲染时重新渲染
export const UserMenuButton = React.memo(function UserMenuButton({
  userEmail,
  showUserMenu,
  onToggleMenu,
  onChangePassword,
  onLogout,
  userMenuRef
}: UserMenuButtonProps) {
  console.log("UserMenuButton is rendering"); // 性能优化后，这个只会在必要时打印

  return (
    <div className="relative" ref={userMenuRef}>
      <Button
        onClick={onToggleMenu}
        variant="outline"
        className="group relative bg-gradient-to-br from-white/95 via-gray-50/90 to-white/95 backdrop-blur-xl border-2 border-gray-200/60 hover:border-indigo-300/70 hover:bg-gradient-to-br hover:from-indigo-50/80 hover:via-purple-50/70 hover:to-blue-50/80 px-3 py-2 lg:px-4 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 flex items-center gap-2 lg:gap-3 transform hover:scale-105 hover:-translate-y-0.5 overflow-hidden min-w-[160px] max-w-[200px] lg:min-w-[180px] lg:max-w-[240px]"
      >
        {/* Animated background overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl" />

        {/* Shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-shimmer" />

        {/* Enhanced User Icon */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500" />
          <div className="relative w-4 h-4 lg:w-6 lg:h-6 bg-gradient-to-br from-indigo-500 via-purple-500 to-blue-600 rounded-full flex items-center justify-center group-hover:rotate-12 transition-all duration-500 shadow-md group-hover:shadow-lg">
            <User className="w-2.5 h-2.5 lg:w-3.5 lg:h-3.5 text-white drop-shadow-sm" />

            {/* Status indicator */}
            <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border border-white shadow-sm">
              <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75" />
            </div>
          </div>
        </div>

        {/* Enhanced Text Display */}
        <div className="relative hidden md:flex flex-col items-start flex-1 min-w-0">
          <span
            className="text-sm lg:text-base font-semibold bg-gradient-to-r from-gray-700 via-indigo-700 to-purple-700 bg-clip-text text-transparent group-hover:from-indigo-600 group-hover:via-purple-600 group-hover:to-blue-600 transition-all duration-500 leading-tight w-full truncate overflow-hidden text-ellipsis whitespace-nowrap"
            title={userEmail}
          >
            {userEmail}
          </span>

          {/* Subtle underline animation */}
          <div className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 via-purple-400 to-blue-400 group-hover:w-full transition-all duration-700 rounded-full" />

          {/* Status text */}
          <span className="text-xs text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 font-medium">
            在线
          </span>
        </div>

        {/* Dropdown Arrow with enhanced styling */}
        <div className="relative ml-1">
          <svg
            className={`w-3 h-3 lg:w-4 lg:h-4 text-gray-400 group-hover:text-indigo-500 transition-all duration-500 ${showUserMenu ? "rotate-180" : ""}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {/* Floating particles effect */}
        <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-indigo-400 rounded-full animate-bounce"
              style={{
                left: `${20 + i * 25}%`,
                top: `${30 + i * 15}%`,
                animationDelay: `${i * 0.2}s`,
                animationDuration: "2s",
              }}
            />
          ))}
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-500 -z-10" />
      </Button>

      {showUserMenu && (
        <div className="absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-xl shadow-xl z-50 overflow-hidden">
          <div className="p-2">
            <button
              onClick={onChangePassword}
              className="w-full flex items-center gap-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200 mb-1"
            >
              <Key className="w-4 h-4" />
              修改密码
            </button>
            <button
              onClick={onLogout}
              className="w-full flex items-center gap-3 px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
            >
              <LogOut className="w-4 h-4" />
              退出登录
            </button>
          </div>
        </div>
      )}
    </div>
  );
});
