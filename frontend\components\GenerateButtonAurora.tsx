"use client"

import React from 'react';

interface GenerateButtonAuroraProps {
  className?: string;
}

// 🔧 性能优化：自定义比较函数，避免因为className变化导致的重渲染
const arePropsEqual = (prevProps: GenerateButtonAuroraProps, nextProps: GenerateButtonAuroraProps) => {
  return prevProps.className === nextProps.className;
};

// 使用 React.memo 包裹Aurora按钮组件 - 防止父组件重渲染时重新渲染
const GenerateButtonAurora = React.memo(function GenerateButtonAurora({
  className = ""
}: GenerateButtonAuroraProps) {
  console.log("GenerateButtonAurora is rendering"); // 性能优化后，这个只会在className变化时打印
  return (
    <>
      {/* Aurora Background Layers */}
      <div className={`absolute inset-0 aurora-layer-1 ${className}`}></div>
      <div className={`absolute inset-0 aurora-layer-2 ${className}`}></div>
      <div className={`absolute inset-0 aurora-layer-3 ${className}`}></div>
      <div className={`absolute inset-0 aurora-layer-4 ${className}`}></div>

      <style jsx>{`
        .aurora-layer-1 {
          background: linear-gradient(
            45deg,
            rgba(75, 112, 233, 0.8),
            rgba(59, 130, 246, 0.6),
            rgba(139, 92, 246, 0.7),
            rgba(201, 100, 57, 0.5)
          );
          background-size: 400% 400%;
          animation: aurora-flow-1 8s ease-in-out infinite;
          border-radius: inherit;
          /* 🔧 性能优化：GPU加速和渲染隔离 */
          will-change: background-position, transform;
          contain: strict;
          transform: translateZ(0);
          backface-visibility: hidden;
        }
        
        .aurora-layer-2 {
          background: linear-gradient(
            -45deg,
            rgba(59, 130, 246, 0.6),
            rgba(139, 92, 246, 0.4),
            rgba(75, 112, 233, 0.7),
            rgba(201, 100, 57, 0.5)
          );
          background-size: 350% 350%;
          animation: aurora-flow-2 12s ease-in-out infinite reverse;
          border-radius: inherit;
          /* 🔧 性能优化：GPU加速和渲染隔离 */
          will-change: background-position, opacity;
          contain: strict;
          transform: translateZ(0);
          backface-visibility: hidden;
        }
        
        .aurora-layer-3 {
          background: linear-gradient(
            90deg,
            rgba(139, 92, 246, 0.5),
            rgba(75, 112, 233, 0.6),
            rgba(59, 130, 246, 0.4),
            rgba(201, 100, 57, 0.7)
          );
          background-size: 300% 300%;
          animation: aurora-flow-3 10s ease-in-out infinite;
          border-radius: inherit;
          /* 🔧 性能优化：GPU加速和渲染隔离 */
          will-change: background-position, transform;
          contain: strict;
          transform: translateZ(0);
          backface-visibility: hidden;
        }
        
        .aurora-layer-4 {
          background: radial-gradient(
            ellipse at center,
            rgba(139, 92, 246, 0.3) 0%,
            rgba(75, 112, 233, 0.4) 25%,
            rgba(59, 130, 246, 0.3) 50%,
            rgba(201, 100, 57, 0.2) 75%,
            transparent 100%
          );
          background-size: 200% 200%;
          animation: aurora-pulse 6s ease-in-out infinite alternate;
          border-radius: inherit;
          /* 🔧 性能优化：GPU加速和渲染隔离 */
          will-change: background-position, opacity, transform;
          contain: strict;
          transform: translateZ(0);
          backface-visibility: hidden;
        }
        
        @keyframes aurora-flow-1 {
          0%, 100% {
            background-position: 0% 50%;
            transform: translateX(0) scale(1);
          }
          25% {
            background-position: 100% 0%;
            transform: translateX(2px) scale(1.02);
          }
          50% {
            background-position: 100% 100%;
            transform: translateX(0) scale(1);
          }
          75% {
            background-position: 0% 100%;
            transform: translateX(-2px) scale(0.98);
          }
        }
        
        @keyframes aurora-flow-2 {
          0%, 100% {
            background-position: 100% 0%;
            opacity: 0.8;
          }
          33% {
            background-position: 0% 100%;
            opacity: 0.6;
          }
          66% {
            background-position: 100% 100%;
            opacity: 0.9;
          }
        }
        
        @keyframes aurora-flow-3 {
          0%, 100% {
            background-position: 0% 0%;
            transform: rotate(0deg);
          }
          50% {
            background-position: 100% 100%;
            transform: rotate(1deg);
          }
        }
        
        @keyframes aurora-pulse {
          0% {
            background-position: 0% 0%;
            opacity: 0.4;
            transform: scale(1);
          }
          50% {
            background-position: 100% 100%;
            opacity: 0.8;
            transform: scale(1.05);
          }
          100% {
            background-position: 0% 0%;
            opacity: 0.4;
            transform: scale(1);
          }
        }
      `}</style>
    </>
  );
}, arePropsEqual);

export default GenerateButtonAurora;
