"use client"

import { useState, useR<PERSON>, useEffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Slider } from "@/components/ui/slider"
import RichTextInput from "./components/ui/rich-text-input"
import { Input } from "@/components/ui/input"
import { Play, Pause, Download, Mic, Volume2, Sparkles, ChevronDown, Zap, Settings, User, LogOut, AlertTriangle, Clock, CreditCard, Key, Eye, EyeOff, CheckCircle, Cpu, Search, X, Filter, Globe, Users, UserCheck, Heart } from "lucide-react"
import BatchOperations from "@/components/BatchOperations"
import { SmartDialogueList } from "@/components/VirtualDialogueList"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { auth, ttsService, autoTagService } from "@/lib/auth-service"
import { TokenManager } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { handleAuthError, getSafeUserFriendlyMessage } from "@/lib/error-utils"
// import AudioAuroraEffect, { AudioAuroraPresets } from "@/components/AudioAuroraEffect"
import GenerateButtonAurora from "@/components/GenerateButtonAurora"
import { VoiceSelectionList } from "@/components/VoiceSelectionList"
import { DialogueLine } from "@/components/DialogueLine"
import { useVoices } from "@/hooks/useVoices"
import { VoicesLoading, VoicesError } from "@/components/VoicesLoadingStates"
import TaskCenter, { TaskCenterRef } from "@/components/TaskCenter"
import { FloatingParticles, AnimatedBackgroundBlobs } from "@/components/ui/BackgroundDecorations"
import { RechargeButton } from "@/components/ui/RechargeButton"
import { UserMenuButton } from "@/components/ui/UserMenuButton"
import { ParameterSliders } from "@/components/ui/ParameterSliders"
import { ModelSelector } from "@/components/ui/ModelSelector"

// 性别符号图标组件（用于标签显示）
const MaleIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <span className="text-white-600 font-bold text-lg">♂</span>
  </div>
);

const FemaleIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <span className="text-white-600 font-bold text-lg">♀</span>
  </div>
);

const NeutralIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <span className="text-gray-600 font-bold text-lg">⚲</span>
  </div>
);

// 筛选下拉选项专用的3D图标组件
const MaleSelectIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://img.icons8.com/3d-fluency/94/person-male--v3.png"
      alt="Male"
      className="w-4 h-4 object-cover"
    />
  </div>
);

const FemaleSelectIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://img.icons8.com/3d-fluency/94/person-female--v3.png"
      alt="Female"
      className="w-4 h-4 object-cover"
    />
  </div>
);

const NeutralSelectIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://img.icons8.com/3d-fluency/94/person--v3.png"
      alt="Neutral"
      className="w-4 h-4 object-cover"
    />
  </div>
);

// 国旗图标组件
const USFlagIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/us.svg"
      alt="US Flag"
      className="w-4 h-3 object-cover rounded-sm"
    />
  </div>
);

const JPFlagIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/jp.svg"
      alt="JP Flag"
      className="w-4 h-3 object-cover rounded-sm"
    />
  </div>
);

const ESFlagIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/es.svg"
      alt="ES Flag"
      className="w-4 h-3 object-cover rounded-sm"
    />
  </div>
);

const KRFlagIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/kr.svg"
      alt="KR Flag"
      className="w-4 h-3 object-cover rounded-sm"
    />
  </div>
);

const FRFlagIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/fr.svg"
      alt="FR Flag"
      className="w-4 h-3 object-cover rounded-sm"
    />
  </div>
);

const GlobalIcon = ({ className }: { className?: string }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <img
      src="https://img.icons8.com/pulsar-gradient/48/infinity.png"
      alt="Infinity"
      className="w-4 h-4 object-contain"
    />
  </div>
);

// 自定义下拉选择组件
interface CustomSelectOption {
  value: string;
  label: string;
  icon: React.ReactNode;
}

interface CustomSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: CustomSelectOption[];
  className?: string;
  hoverColor?: "green" | "orange";
}

const CustomSelect: React.FC<CustomSelectProps> = ({ value, onChange, options, className = "", hoverColor = "green" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const hoverColorClasses = {
    green: "group-hover:text-green-500 focus:border-green-400 focus:ring-green-500/10 hover:border-green-300",
    orange: "group-hover:text-orange-500 focus:border-orange-400 focus:ring-orange-500/10 hover:border-orange-300"
  };

  const bgColorClasses = {
    green: "from-green-500/5 to-blue-500/5",
    orange: "from-orange-500/5 to-red-500/5"
  };

  return (
    <div className="relative group" ref={dropdownRef}>
      <div className={`absolute inset-0 bg-gradient-to-r ${bgColorClasses[hoverColor]} rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300`}></div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative w-full px-4 py-3 text-sm border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl hover:border-gray-300 ${hoverColorClasses[hoverColor]} transition-all duration-300 outline-none cursor-pointer shadow-sm hover:shadow-lg font-medium text-gray-700 flex items-center justify-between`}
      >
        <div className="flex items-center gap-3">
          <span className={`text-gray-400 ${hoverColorClasses[hoverColor]} transition-colors duration-300`}>
            {selectedOption?.icon}
          </span>
          <span>{selectedOption?.label}</span>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 ${hoverColorClasses[hoverColor]} transition-all duration-300 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                onChange(option.value);
                setIsOpen(false);
              }}
              className={`w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-all duration-200 flex items-center gap-3 ${
                value === option.value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
              }`}
            >
              <span className={`${value === option.value ? 'text-blue-600' : 'text-gray-400'} transition-colors duration-200`}>
                {option.icon}
              </span>
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default function AITTSWorkspace() {
  // 临时开关：设置为 true 时临时开放多人对话功能，不限制PRO会员
  const TEMP_DISABLE_PRO_RESTRICTION = true;

  // 使用声音数据管理Hook
  const { voices, isLoading: isLoadingVoices, error: voicesError, voiceIconMapping, retry: retryVoices } = useVoices();

  // 声音图标数组 - 用于组件中需要fallback的地方
  const voiceIcons = [
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg",
    "https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"
  ];

  const [text, setText] = useState("")
  const [selectedVoice, setSelectedVoice] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [hasAudio, setHasAudio] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [stability, setStability] = useState([0.58])
  const [similarity, setSimilarity] = useState([0.75])
  const [style, setStyle] = useState([0.3])
  const [speechRate, setSpeechRate] = useState([1])
  const [textareaFocused, setTextareaFocused] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [selectedModel, setSelectedModel] = useState("eleven_multilingual_v2")
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false)
  const [isPageLoaded, setIsPageLoaded] = useState(false)
  const [userEmail, setUserEmail] = useState("")
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [audioBuffer, setAudioBuffer] = useState<ArrayBuffer | null>(null)
  const [audioUrls, setAudioUrls] = useState<{
    streamUrl: string | null;
    downloadUrl: string | null;
    secureStreamUrl: string | null; // 新增：安全的blob URL用于播放
  }>({
    streamUrl: null,
    downloadUrl: null,
    secureStreamUrl: null
  })
  const [audioDuration, setAudioDuration] = useState<string>("00:00")
  const [currentTime, setCurrentTime] = useState<string>("00:00")
  const [currentExampleIndex, setCurrentExampleIndex] = useState(0)
  const [progress, setProgress] = useState<number>(0)
  const [isDragging, setIsDragging] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [showVipDialog, setShowVipDialog] = useState(false)
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const [currentPreviewAudio, setCurrentPreviewAudio] = useState<HTMLAudioElement | null>(null)
  const [previewingVoice, setPreviewingVoice] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const { toast } = useToast()

  // 安全的音频加载函数 - 使用Authorization header
  const loadSecureAudio = async (streamUrl: string): Promise<string | null> => {
    try {
      const token = TokenManager.getAccessToken()
      if (!token) {
        console.error('[SECURE-AUDIO] No token available for audio loading')
        return null
      }

      console.log('[SECURE-AUDIO] Loading audio with Authorization header:', streamUrl)

      const response = await fetch(streamUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'audio/mpeg'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)

      console.log('[SECURE-AUDIO] Audio loaded successfully, blob URL created')
      return blobUrl
    } catch (error) {
      console.error('[SECURE-AUDIO] Failed to load audio:', error)
      return null
    }
  }

  // 安全的音频下载函数 - 使用Authorization header
  const downloadSecureAudio = async (downloadUrl: string, fileName: string): Promise<boolean> => {
    try {
      const token = TokenManager.getAccessToken()
      if (!token) {
        console.error('[SECURE-DOWNLOAD] No token available for download')
        setError('认证失败，请重新登录')
        return false
      }

      console.log('[SECURE-DOWNLOAD] Downloading audio with Authorization header:', downloadUrl)

      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/octet-stream'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理blob URL
      URL.revokeObjectURL(blobUrl)

      console.log('[SECURE-DOWNLOAD] Download completed successfully')
      return true
    } catch (error) {
      console.error('[SECURE-DOWNLOAD] Download failed:', error)
      setError('下载失败，请检查网络连接或重试')
      return false
    }
  }
  const [showProUpgradeDialog, setShowProUpgradeDialog] = useState(false)

  // 字符限制提示状态
  const [showCharLimitToast, setShowCharLimitToast] = useState(false)
  const [charLimitMessage, setCharLimitMessage] = useState("")

  // 用户状态管理
  const [userStatus, setUserStatus] = useState<{
    isVip: boolean;
    type?: string;
    expireAt: number;
  }>({
    isVip: false,
    expireAt: 0
  })

  // 声音搜索和筛选状态
  const [searchQuery, setSearchQuery] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [selectedGender, setSelectedGender] = useState<"all" | "male" | "female" | "neutral">("all")
  const [selectedLanguage, setSelectedLanguage] = useState<"all" | "en" | "ja" | "es" | "ko" | "fr">("all")
  const [showFilterModal, setShowFilterModal] = useState(false)

  // 【新增】声音收藏相关状态
  const [favoriteVoiceIds, setFavoriteVoiceIds] = useState<string[]>([])
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false)

  // 模态框内的临时筛选状态
  const [tempSelectedGender, setTempSelectedGender] = useState<"all" | "male" | "female" | "neutral">("all")
  const [tempSelectedLanguage, setTempSelectedLanguage] = useState<"all" | "en" | "ja" | "es" | "ko" | "fr">("all")
  const [tempSearchQuery, setTempSearchQuery] = useState("")
  const [tempSelectedVoice, setTempSelectedVoice] = useState<string>("")
  // 【新增】模态框内的临时收藏筛选状态
  const [tempShowOnlyFavorites, setTempShowOnlyFavorites] = useState(false)

  // 搜索防抖处理
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // 异步TTS相关状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  const [taskStatus, setTaskStatus] = useState<'idle' | 'processing' | 'complete' | 'failed'>('idle')
  const [taskProgress, setTaskProgress] = useState<string>('')

  // 【新增】逻辑任务管理状态 - 解决重试时任务重复创建问题
  const [logicalTaskId, setLogicalTaskId] = useState<string | null>(null)
  const [physicalTaskIds, setPhysicalTaskIds] = useState<string[]>([])
  const logicalTaskIdRef = useRef<string | null>(null)
  // 注意：isInitialRequest 已移除，现在完全依赖 logicalTaskIdRef 进行去重判断

  // 【优化】定义任务映射的数据结构
  interface TaskMapping {
    logicalTaskId: string;
    displayTaskId: string; // 在任务中心显示的ID，通常是第一个物理ID
    physicalTaskIds: string[];
    createdAt: number;
  }

  const MAPPING_STORAGE_KEY = 'tts_task_mapping'

  // 【新增】数据中心切换重试相关状态
  const [retryCount, setRetryCount] = useState<number>(0)
  const [excludedLocations, setExcludedLocations] = useState<string[]>([])
  const [originalTaskData, setOriginalTaskData] = useState<any>(null)
  const MAX_RETRY_ATTEMPTS = 1 // 最大重试次数

  // 【新增】数据分层保护 - 第二层：useRef备份
  const originalTaskDataRef = useRef<any>(null)

  // 【新增】数据分层保护 - 第三层：sessionStorage备份
  const TASK_DATA_KEY = 'tts_retry_task_data'

  // 【新增】增强型重试状态管理 - 统一的重试状态
  const [retryState, setRetryState] = useState({
    // 前端重试相关
    frontendAttempts: 0,           // 当前已尝试次数
    frontendMaxAttempts: 1,        // 最大重试次数
    frontendInProgress: false,     // 是否正在前端重试
    frontendStartTime: 0,          // 重试开始时间

    // 数据中心重试相关
    datacenterAttempts: 0,         // 数据中心重试次数
    datacenterMaxAttempts: 1,      // 数据中心最大重试次数

    // 【增强】备用API重试相关 - 支持多个备用API
    backupApiAttempts: 0,          // 备用API重试次数
    backupApiMaxAttempts: 2,       // 备用API最大重试次数
    usingBackupApi: false,         // 当前是否使用备用API
    backupApiStartTime: 0,         // 备用API重试开始时间
    currentBackupIndex: -1,        // 当前使用的备用API索引 (-1表示未使用备用API)
    maxBackupIndex: -1,            // 最大可用的备用API索引

    // 通用状态
    isLocked: false,               // 状态是否被锁定
    lockReason: '',                // 锁定原因
    activeRetryType: null as 'frontend' | 'datacenter' | 'backup' | null,

    // 错误历史
    errorHistory: [] as Array<{
      timestamp: number,
      type: string,
      message: string,
      retryType: 'frontend' | 'datacenter' | 'backup'
    }>
  })



  // pollingInterval state is no longer needed and will be removed.
  // const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)

  // 新增: 用于在回调中获取最新状态
  const taskStatusRef = useRef(taskStatus);
  useEffect(() => {
    taskStatusRef.current = taskStatus;
  }, [taskStatus]);

  // 修改密码相关状态
  const [showChangePasswordDialog, setShowChangePasswordDialog] = useState(false)
  const [changePasswordData, setChangePasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [changePasswordError, setChangePasswordError] = useState("")
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [showPasswordSuccessDialog, setShowPasswordSuccessDialog] = useState(false)

  // 公告弹窗状态（字数限制通知）
  const [showNoticeDialog, setShowNoticeDialog] = useState(false)

  // 【新增】处理"不再提示"按钮点击的函数
  const handleHideNoticePermanently = () => {
    try {
      // 在 localStorage 中设置一个标记，值为字符串 'true'
      localStorage.setItem('hideContentPolicyNotice', 'true');
      console.log("用户已选择永久隐藏内容政策通知。");
    } catch (error) {
      // 在一些非常旧的浏览器或存储被禁用的情况下可能会发生错误
      console.error("无法在 localStorage 中保存设置:", error);
    }
    // 无论是否保存成功，都关闭当前弹窗
    setShowNoticeDialog(false);
  };

  // 【新增】收藏相关工具函数
  const saveFavoritesToStorage = (voiceIds: string[]) => {
    try {
      localStorage.setItem('favoriteVoices', JSON.stringify({
        favoriteVoiceIds: voiceIds,
        lastUpdated: Date.now()
      }));
    } catch (error) {
      console.error("无法保存收藏到 localStorage:", error);
    }
  };

  const loadFavoritesFromStorage = (): string[] => {
    try {
      const saved = localStorage.getItem('favoriteVoices');
      if (saved) {
        const data = JSON.parse(saved);
        return data.favoriteVoiceIds || [];
      }
    } catch (error) {
      console.error("无法从 localStorage 加载收藏:", error);
    }
    return [];
  };

  const toggleVoiceFavorite = (voiceId: string) => {
    // 先检查当前状态，避免在setState回调中调用toast
    const isCurrentlyFavorited = favoriteVoiceIds.includes(voiceId);

    setFavoriteVoiceIds(prev => {
      const newFavorites = prev.includes(voiceId)
        ? prev.filter(id => id !== voiceId)
        : [...prev, voiceId];

      // 保存到localStorage
      saveFavoritesToStorage(newFavorites);

      return newFavorites;
    });

    // 在状态更新之外显示操作反馈
    toast({
      title: isCurrentlyFavorited ? "已取消收藏" : "已添加收藏",
      description: isCurrentlyFavorited
        ? "声音已从收藏中移除"
        : "声音已添加到收藏",
      duration: 2000,
    });
  };

  // v3模型功能介绍弹窗状态
  const [showV3IntroDialog, setShowV3IntroDialog] = useState(false)

  // 自动生成标签相关状态
  const [isGeneratingTags, setIsGeneratingTags] = useState(false)
  const [tagGenerationError, setTagGenerationError] = useState<string | null>(null)

  // 新增: 对话模式状态
  const [mode, setMode] = useState<'single' | 'dialogue'>('single')
  const [dialogueLines, setDialogueLines] = useState([
    { id: 1, voice: "", text: "" }, // 使用固定ID避免水合失败
  ])
  // 新增: 追踪正在编辑的对话行 - 重构为激活状态
  const [activeDialogueLineId, setActiveDialogueLineId] = useState<number | null>(null)
  // 保持向后兼容
  const [editingDialogueLineId, setEditingDialogueLineId] = useState<number | null>(null)
  // 新增: 防止快速连续添加对话行
  const [isAddingDialogueLine, setIsAddingDialogueLine] = useState(false)

  const dropdownRef = useRef<HTMLDivElement>(null)
  const dropdownScrollRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const modelDropdownRef = useRef<HTMLDivElement>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const dialogueVoiceMenuRef = useRef<HTMLDivElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const isDraggingRef = useRef(isDragging)
  const autoplayNextRef = useRef(false)
  const taskCenterRef = useRef<TaskCenterRef>(null)
  // 【新增】使用ref避免WebSocket回调中的闭包问题
  const currentTaskIdRef = useRef<string | null>(null)
  // 根据选择的模型动态设置最大字符数
  const maxChars = useMemo(() => {
    if (selectedModel === "eleven_v3") {
      return 3000; // v3模型限制3000字符
    }
    return 5000; // 其他模型限制5000字符
  }, [selectedModel])

  // 声音筛选逻辑 - 主界面使用
  const filteredVoices = useMemo(() => {
    let filtered = voices;

    // 性别筛选
    if (selectedGender !== "all") {
      filtered = filtered.filter(voice => voice.gender === selectedGender)
    }

    // 语言筛选
    if (selectedLanguage !== "all") {
      filtered = filtered.filter(voice => voice.language === selectedLanguage)
    }

    // 搜索筛选 - 使用防抖后的查询
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase().trim()
      filtered = filtered.filter(voice =>
        voice.name.toLowerCase().includes(query) ||
        voice.description.toLowerCase().includes(query)
      )
    }

    // 【新增】收藏筛选
    if (showOnlyFavorites) {
      filtered = filtered.filter(voice => favoriteVoiceIds.includes(voice.id))
    }

    return filtered
  }, [voices, selectedGender, selectedLanguage, debouncedSearchQuery, showOnlyFavorites, favoriteVoiceIds])

  // 模态框内的临时筛选逻辑
  const tempFilteredVoices = useMemo(() => {
    let filtered = voices;

    // 性别筛选
    if (tempSelectedGender !== "all") {
      filtered = filtered.filter(voice => voice.gender === tempSelectedGender)
    }

    // 语言筛选
    if (tempSelectedLanguage !== "all") {
      filtered = filtered.filter(voice => voice.language === tempSelectedLanguage)
    }

    // 搜索筛选
    if (tempSearchQuery.trim()) {
      const query = tempSearchQuery.toLowerCase().trim()
      filtered = filtered.filter(voice =>
        voice.name.toLowerCase().includes(query) ||
        voice.description.toLowerCase().includes(query)
      )
    }

    // 【新增】收藏筛选
    if (tempShowOnlyFavorites) {
      filtered = filtered.filter(voice => favoriteVoiceIds.includes(voice.id))
    }

    return filtered
  }, [voices, tempSelectedGender, tempSelectedLanguage, tempSearchQuery, tempShowOnlyFavorites, favoriteVoiceIds])

  const emotionalTagCount = useMemo(() => {
    const regex = /\[([a-zA-Z\s]+)\]/g;
    const count = (text.match(regex) || []).length;
    if (count === 0) return null;
    return (
      <div className="absolute -top-8 right-2 bg-purple-100 text-purple-700 text-sm font-semibold px-2.5 py-1 rounded-lg z-10">
        情感标注词 {count}个
      </div>
    )
  }, [text]);

  // 打开筛选模态框时，同步当前筛选状态到临时状态
  const openFilterModal = () => {
    setTempSelectedGender(selectedGender)
    setTempSelectedLanguage(selectedLanguage)
    setTempSearchQuery(searchQuery)
    setTempSelectedVoice(selectedVoice)
    // 【新增】同步收藏筛选状态
    setTempShowOnlyFavorites(showOnlyFavorites)
    setShowFilterModal(true)
  }

  // 应用筛选 - 将临时状态应用到主状态
  const applyFilters = () => {
    setSelectedGender(tempSelectedGender)
    setSelectedLanguage(tempSelectedLanguage)
    setSearchQuery(tempSearchQuery)
    // 【新增】应用收藏筛选状态
    setShowOnlyFavorites(tempShowOnlyFavorites)

    // 应用临时选中的声音，如果有的话
    if (tempSelectedVoice) {
      setSelectedVoice(tempSelectedVoice)
    } else {
      // 如果没有临时选中的声音，检查当前选中的声音是否在筛选结果中
      const newFilteredVoices = voices.filter(voice => {
        // 性别筛选
        if (tempSelectedGender !== "all" && voice.gender !== tempSelectedGender) {
          return false
        }
        // 语言筛选
        if (tempSelectedLanguage !== "all" && voice.language !== tempSelectedLanguage) {
          return false
        }
        // 搜索筛选
        if (tempSearchQuery.trim()) {
          const query = tempSearchQuery.toLowerCase().trim()
          if (!voice.name.toLowerCase().includes(query) && !voice.description.toLowerCase().includes(query)) {
            return false
          }
        }
        // 【新增】收藏筛选
        if (tempShowOnlyFavorites && !favoriteVoiceIds.includes(voice.id)) {
          return false
        }
        return true
      })

      // 如果当前选中的声音不在新的筛选结果中，选择第一个可用的声音
      if (newFilteredVoices.length > 0 && !newFilteredVoices.find(voice => voice.id === selectedVoice)) {
        setSelectedVoice(newFilteredVoices[0].id)
      }
    }

    setShowFilterModal(false)
  }

  // 重置筛选
  const resetFilters = () => {
    setTempSelectedGender("all")
    setTempSelectedLanguage("all")
    setTempSearchQuery("")
    // 【新增】重置收藏筛选
    setTempShowOnlyFavorites(false)
    // 不重置临时选中的声音，保持用户的选择
  }

  // 🔧 性能优化：使用useMemo包裹models数组，避免每次重渲染都创建新数组
  const models = useMemo(() => [
    {
      id: "eleven_v3",
      name: "Eleven v3 (测试)支持情感标注慢速",
      description: "最具表现力的模型，支持70多种语言。相比以往的模型，需要更多的提示工程。目前处于 Alpha 阶段，可靠性将随着时间推移不断提升",
      icon: (
        <svg width="33" height="33" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="transition-all duration-200 ease-in-out">
          <rect x="0" y="0" width="32" height="32" rx="16" fill="#5D79DF" stroke="#F3F4F6" strokeWidth="2.5" className="group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"/>
          <path d="M16.7527 21.9441C16.6291 22.2951 16.1328 22.2951 16.0093 21.9441L14.9179 18.8442C14.8458 18.6395 14.6219 18.5316 14.417 18.6027L11.4214 19.642C11.0768 19.7615 10.7687 19.3892 10.9506 19.0731L12.6191 16.1737C12.7216 15.9955 12.6699 15.7684 12.5003 15.6521L9.7946 13.7969C9.4974 13.5931 9.6069 13.1303 9.9639 13.0814L13.1369 12.6469C13.3456 12.6183 13.4949 12.4307 13.4759 12.2209L13.1743 8.8874C13.141 8.519 13.5876 8.3118 13.8474 8.5751L16.1004 10.8595C16.2547 11.016 16.5072 11.016 16.6616 10.8595L18.9146 8.5751C19.1743 8.3118 19.621 8.519 19.5877 8.8874L19.2861 12.2209C19.2671 12.4307 19.4164 12.6183 19.6251 12.6469L22.798 13.0814C23.155 13.1303 23.2646 13.5931 22.9674 13.7969L20.2616 15.6521C20.0921 15.7684 20.0404 15.9955 20.1429 16.1737L21.8113 19.0731C21.9932 19.3892 21.6852 19.7615 21.3406 19.642L18.345 18.6027C18.14 18.5316 17.9161 18.6395 17.8441 18.8442L16.7527 21.9441Z" stroke="white" strokeWidth="1.5" fill="none"/>
        </svg>
      )
    },
    {
      id: "eleven_multilingual_v2",
      name: "Eleven Multilingual v2",
      description: "最具真实感、情感丰富的模式，支持29种语言。非常适合配音、有声书、后期制作或其他内容创作需求",
      icon: (
        <svg width="33" height="33" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="transition-all duration-200 ease-in-out">
          <defs>
            <linearGradient id="multilingual-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#F99BFF" />
              <stop offset="100%" stopColor="#9B59FF" />
            </linearGradient>
          </defs>
          <rect x="0" y="0" width="32" height="32" rx="16" fill="url(#multilingual-gradient)" stroke="#F3F4F6" strokeWidth="2.5" className="group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"/>
          {/* 人脸侧影 */}
          <path d="M12 10C13.5 10 14.5 11 14.5 12.5V13.5C14.5 14 14.8 14.3 15.3 14.3C15.8 14.3 16.1 14 16.1 13.5V12C16.1 10.3 14.8 9 13.1 9C11.4 9 10.1 10.3 10.1 12V16C10.1 17.7 11.4 19 13.1 19H13.5" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          {/* 嘴部 */}
          <path d="M13.5 16.5C13.8 16.8 14.2 17 14.7 17" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          {/* 声波 - 三条弧线 */}
          <path d="M18 12C18.5 12.5 18.8 13.2 18.8 14C18.8 14.8 18.5 15.5 18 16" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M20 10C21 11 21.5 12.4 21.5 14C21.5 15.6 21 17 20 18" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M22 8C23.5 9.5 24.2 11.6 24.2 14C24.2 16.4 23.5 18.5 22 20" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      id: "eleven_turbo_v2_5",
      name: "Eleven Turbo v2.5",
      description: "高质量、低延迟模型支持32种语言。非常适合在需要速度且需要使用非英语语言的使用场景",
      icon: (
        <svg width="33" height="33" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="transition-all duration-200 ease-in-out">
          <defs>
            <linearGradient id="turbo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#10B981" />
              <stop offset="100%" stopColor="#059669" />
            </linearGradient>
          </defs>
          <rect x="0" y="0" width="32" height="32" rx="16" fill="url(#turbo-gradient)" stroke="#F3F4F6" strokeWidth="2.5" className="group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"/>
          {/* 闪电符号 - 代表速度 */}
          <path d="M18 8L12 16H16L14 24L20 16H16L18 8Z" fill="white"/>
          {/* 速度线条 - 代表快速 */}
          <path d="M22 10H26" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M23 13H25" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M22 19H26" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M23 22H25" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
        </svg>
      )
    },
    {
      id: "eleven_turbo_v2",
      name: "Eleven Turbo v2",
      description: "仅支持英文的低延迟模型，在需要速度且仅需英文的情况下表现最佳。性能与 Turbo v2.5 相当",
      icon: (
        <svg width="33" height="33" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="transition-all duration-200 ease-in-out">
          <defs>
            <linearGradient id="turbo-v2-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="100%" stopColor="#1D4ED8" />
            </linearGradient>
          </defs>
          <rect x="0" y="0" width="32" height="32" rx="16" fill="url(#turbo-v2-gradient)" stroke="#F3F4F6" strokeWidth="2.5" className="group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"/>
          {/* 火箭图标 - 代表极速 */}
          <path d="M16 6L18 12H20L16 26L12 12H14L16 6Z" fill="white"/>
          {/* 火箭尾焰 */}
          <path d="M14 12C13.5 13 13.5 14 14 15" stroke="white" strokeWidth="1.2" strokeLinecap="round"/>
          <path d="M18 12C18.5 13 18.5 14 18 15" stroke="white" strokeWidth="1.2" strokeLinecap="round"/>
          {/* 英文字母 "EN" - 表示仅支持英文 */}
          <text x="16" y="21" textAnchor="middle" fill="white" fontSize="6" fontWeight="bold" fontFamily="Arial, sans-serif">EN</text>
        </svg>
      )
    }
  ], []) // models数组是静态的，不依赖任何状态

  // 根据当前模式筛选可用的模型
  const availableModels = useMemo(() => {
    if (mode === 'dialogue') {
      // 多人对话模式只显示 Eleven v3 模型
      return models.filter(model => model.id === 'eleven_v3')
    }
    // 单人模式显示所有模型
    return models
  }, [mode])

  useEffect(() => {
    isDraggingRef.current = isDragging
  }, [isDragging])

  // 客户端挂载状态管理 - 解决水合失败问题
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 声音数据加载完成后的初始化
  useEffect(() => {
    if (voices.length > 0 && !selectedVoice) {
      const firstVoiceId = voices[0].id;
      setSelectedVoice(firstVoiceId);

      // 更新对话行的默认声音（如果当前对话行的声音为空）
      setDialogueLines(prevLines =>
        prevLines.map(line =>
          line.voice === "" ? { ...line, voice: firstVoiceId } : line
        )
      );
    }
  }, [voices, selectedVoice]);

  // 【新增】页面加载时恢复收藏数据
  useEffect(() => {
    const savedFavorites = loadFavoritesFromStorage();
    setFavoriteVoiceIds(savedFavorites);
  }, []);

  useEffect(() => {
    // 检查登录状态
    const isLoggedIn = auth.isLoggedIn()
    const email = auth.getCurrentUserEmail()

    if (!isLoggedIn) {
      window.location.href = "/login"
      return
    }

    // 设置用户邮箱
    setUserEmail(email || "")
    setIsPageLoaded(true)

    // 获取用户状态
    const fetchUserStatus = async () => {
      try {
        const data = await auth.getUserQuota()
        setUserStatus({
          isVip: data.isVip,
          type: data.type,
          expireAt: data.expireAt
        })
      } catch (error: any) {
        console.error('获取用户状态失败:', error)

        // 【修复】添加认证错误处理逻辑
        const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
          // 认证错误回调：显示登录过期弹窗
          setShowAuthDialog(true)
        })

        if (isAuth && shouldRedirect) {
          // 如果是认证错误且需要跳转，延迟跳转给用户看到弹窗
          setTimeout(() => {
            window.location.href = '/login'
          }, 2000)
        }
      }
    }
    fetchUserStatus()

    // 【修改】页面加载完成后延迟显示公告弹窗（字数限制通知）- 添加localStorage检查
    // 在设置计时器之前，先从 localStorage 获取标记
    const shouldHideNotice = localStorage.getItem('hideContentPolicyNotice');

    // 声明计时器变量，用于后续清理
    let showNoticeTimer: NodeJS.Timeout | null = null;

    // 只有当标记不为 'true' 时，才执行显示弹窗的逻辑
    if (shouldHideNotice !== 'true') {
      showNoticeTimer = setTimeout(() => {
        setShowNoticeDialog(true)
      }, 1000) // 延迟1秒显示，让页面先完全加载
    } else {
      console.log("根据用户偏好，内容政策通知已被隐藏。");
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
        setEditingDialogueLineId(null) // 关闭主下拉菜单时，也取消编辑状态
      }
      if (modelDropdownRef.current && !modelDropdownRef.current.contains(event.target as Node)) {
        setIsModelDropdownOpen(false)
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
      // 筛选模态框不需要点击外部关闭，因为它有自己的关闭逻辑
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      if (showNoticeTimer) {
        clearTimeout(showNoticeTimer) // 清理定时器
      }
    }
  }, [])

  // Declarative audio event handling
  useEffect(() => {
    const audio = audioRef.current
    if (!audio || !audioUrls.secureStreamUrl) return

    const handleLoadedMetadata = async () => {
      const duration = audio.duration
      if (duration && !isNaN(duration)) {
        const minutes = Math.floor(duration / 60)
        const seconds = Math.floor(duration % 60)
        setAudioDuration(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`)
      }
      // Check if we should autoplay - 修复竞态条件问题（确保播放器准备就绪）
      if (autoplayNextRef.current) {
        try {
          // 等待播放Promise完成，确保音频真正开始播放
          await audio.play()
          // 只有在播放成功后，才更新UI状态
          setIsPlaying(true)
          autoplayNextRef.current = false // Reset the flag
        } catch (error) {
          // 如果播放失败（例如，数据不足或浏览器限制）
          console.error("Autoplay failed:", error)
          // 保证UI状态与实际一致，重置为未播放状态
          setIsPlaying(false)
          autoplayNextRef.current = false // 同样重置，避免下次重试
        }
      }
    }

    const handleTimeUpdate = () => {
      if (audio && !isDraggingRef.current) {
        const current = audio.currentTime
        const duration = audio.duration

        const minutes = Math.floor(current / 60)
        const seconds = Math.floor(current % 60)
        setCurrentTime(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`)

        if (duration > 0) {
          setProgress((current / duration) * 100)
        }
      }
    }

    const handleAudioError = (event: Event) => {
      console.error('[AUDIO] Failed to load audio:', event);
      console.error('[AUDIO] Secure Stream URL:', audioUrls.secureStreamUrl);
      console.error('[AUDIO] Audio error details:', (event.target as HTMLAudioElement)?.error);

      // 重置播放状态
      setIsPlaying(false);
      setHasAudio(false);
      autoplayNextRef.current = false;

      // 显示用户友好的错误信息
      setError('音频加载失败，请检查网络连接或重新生成');
    }

    audio.addEventListener("loadedmetadata", handleLoadedMetadata)
    audio.addEventListener("timeupdate", handleTimeUpdate)
    audio.addEventListener("error", handleAudioError)

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata)
      audio.removeEventListener("timeupdate", handleTimeUpdate)
      audio.removeEventListener("error", handleAudioError)
    }
  }, [audioUrls.secureStreamUrl])

  // 清理预览音频和blob URL
  useEffect(() => {
    return () => {
      // 清理预览音频
      if (currentPreviewAudio) {
        currentPreviewAudio.pause()
        currentPreviewAudio.currentTime = 0
      }

      // 清理安全的blob URL
      if (audioUrls.secureStreamUrl && audioUrls.secureStreamUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrls.secureStreamUrl)
      }
    }
  }, [currentPreviewAudio, audioUrls.secureStreamUrl])

  useEffect(() => {
    const updateDropdownPosition = () => {
      if (isDropdownOpen && dropdownRef.current) {
        // Force re-render to recalculate position
        const rect = dropdownRef.current.getBoundingClientRect()
        const viewportHeight = window.innerHeight
        const viewportWidth = window.innerWidth

        // Check if dropdown would go off-screen and adjust
        if (rect.bottom + 400 > viewportHeight) {
          // Position above if not enough space below
          setIsDropdownOpen(false)
          setTimeout(() => setIsDropdownOpen(true), 10)
        }
      }
    }

    const handleScroll = () => updateDropdownPosition()
    const handleResize = () => updateDropdownPosition()

    if (isDropdownOpen) {
      window.addEventListener("scroll", handleScroll, { passive: true })
      window.addEventListener("resize", handleResize, { passive: true })

      return () => {
        window.removeEventListener("scroll", handleScroll)
        window.removeEventListener("resize", handleResize)
      }
    }
  }, [isDropdownOpen])

  // 下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (isDropdownOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 100)
    }
  }, [isDropdownOpen])

  // 滚动定位到选中的声音选项 - 适配筛选后的列表
  useEffect(() => {
    if (isDropdownOpen && selectedVoice && dropdownScrollRef.current) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      setTimeout(() => {
        const selectedElement = dropdownScrollRef.current?.querySelector(`[data-voice-id="${selectedVoice}"]`)
        if (selectedElement) {
          selectedElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      }, 50)
    }
  }, [isDropdownOpen, selectedVoice, filteredVoices])

  // 当模式切换时，自动调整模型选择
  useEffect(() => {
    if (mode === 'dialogue') {
      // 切换到多人对话模式时，如果当前模型不是 eleven_v3，自动切换到 eleven_v3
      if (selectedModel !== 'eleven_v3') {
        setSelectedModel('eleven_v3')
      }
    }
    // 注意：从多人对话切换回单人模式时，保持当前选中的模型不变
  }, [mode, selectedModel])

  // 当切换到 eleven_v3 模型时，自动调整稳定性为离散值
  useEffect(() => {
    if (selectedModel === "eleven_v3") {
      const currentStability = stability[0];
      let discreteValue;
      if (currentStability <= 0.25) {
        discreteValue = 0;
      } else if (currentStability <= 0.75) {
        discreteValue = 0.5;
      } else {
        discreteValue = 1;
      }
      // 只有当当前值不是离散值时才更新
      if (currentStability !== discreteValue) {
        setStability([discreteValue]);
      }
    }
  }, [selectedModel, stability])

  // 用ref跟踪上一次的模型，避免无限循环
  const prevSelectedModelRef = useRef(selectedModel);

  // 当模型切换时，检查并处理文本长度超限问题
  useEffect(() => {
    // 只有当模型真正发生变化时才处理
    if (prevSelectedModelRef.current !== selectedModel) {
      const currentMaxChars = selectedModel === "eleven_v3" ? 3000 : 5000;

      if (text.length > currentMaxChars) {
        // 保存原始长度用于提示
        const originalLength = text.length;

        // 自动截取到新的限制长度
        const truncatedText = text.substring(0, currentMaxChars);
        setText(truncatedText);

        // 显示提示信息
        const modelName = selectedModel === "eleven_v3" ? "Eleven v3" : "其他模型";
        const message = `切换到${modelName}模型，字符限制为${currentMaxChars}字符，已自动截取原文本（${originalLength}字符）到${currentMaxChars}字符`;
        setCharLimitMessage(message);
        setShowCharLimitToast(true);

        // 3秒后自动隐藏提示
        setTimeout(() => {
          setShowCharLimitToast(false);
        }, 3000);
      }

      // 更新ref为当前模型
      prevSelectedModelRef.current = selectedModel;
    }
  }, [selectedModel, text])

  // v3模型功能介绍弹窗自动关闭 - 已注释，改为手动关闭
  // useEffect(() => {
  //   let timer: NodeJS.Timeout;
  //   if (showV3IntroDialog) {
  //     timer = setTimeout(() => {
  //       setShowV3IntroDialog(false);
  //     }, 5000); // 5秒后自动关闭
  //   }
  //   return () => {
  //     if (timer) {
  //       clearTimeout(timer);
  //     }
  //   };
  // }, [showV3IntroDialog])

  // 计算生成按钮是否应该被禁用（使用新的重试状态）
  const isGenerateDisabled = useMemo(() => {
    if (isGenerating || retryState.frontendInProgress) return true;
    if (mode === 'single') {
      return !text.trim() || !selectedVoice;
    }
    if (mode === 'dialogue') {
      return dialogueLines.some(line => !line.text.trim() || !line.voice);
    }
    return true;
  }, [mode, isGenerating, retryState.frontendInProgress, text, selectedVoice, dialogueLines]);

  // 当文本内容改变时，清除标签生成错误
  useEffect(() => {
    if (tagGenerationError) {
      setTagGenerationError(null)
    }
  }, [text])

  // 新增：主声音选择器与多人对话声音选择的双向同步 - 重构版本
  useEffect(() => {
    // 当主声音选择器改变时，如果当前有激活的对话行，则同步更新该对话行的声音
    if (activeDialogueLineId !== null && selectedVoice) {
      setDialogueLines(prevLines => {
        // 找到当前激活的行
        const activeLine = prevLines.find(line => line.id === activeDialogueLineId);
        // **核心修复：只有当激活行的声音与主选择器的声音不一致时，才进行更新**
        if (activeLine && activeLine.voice !== selectedVoice) {
          return prevLines.map(line =>
            line.id === activeDialogueLineId ? { ...line, voice: selectedVoice } : line
          );
        }
        // 如果声音已经一致，返回原数组，避免不必要的重渲染
        return prevLines;
      });
    }
    // 保持向后兼容
    if (editingDialogueLineId !== null && selectedVoice) {
      setDialogueLines(prevLines => {
        const editingLine = prevLines.find(line => line.id === editingDialogueLineId);
        if (editingLine && editingLine.voice !== selectedVoice) {
          return prevLines.map(line =>
            line.id === editingDialogueLineId ? { ...line, voice: selectedVoice } : line
          );
        }
        return prevLines;
      });
    }
  }, [selectedVoice, activeDialogueLineId, editingDialogueLineId])

  // 新增：当激活对话行时，同步主声音选择器
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (activeDialogueLineId !== null) {
      // 关键修复：直接从 dialogueLines state 中读取最新的值，但不要将其作为依赖项
      const activeLine = dialogueLines.find(line => line.id === activeDialogueLineId);
      if (activeLine && activeLine.voice !== selectedVoice) {
        setSelectedVoice(activeLine.voice);
      }
    }
  }, [activeDialogueLineId]); // <-- 只依赖 activeDialogueLineId

  // 模式切换处理函数（包含PRO权限检查）
  const handleModeChange = async (newMode: 'single' | 'dialogue') => {
    if (newMode === 'dialogue' && !TEMP_DISABLE_PRO_RESTRICTION) {
      // 检查用户是否为PRO会员
      if (!userStatus.type?.startsWith('P')) {
        setShowProUpgradeDialog(true);
        return; // 阻止模式切换
      }
    }
    setMode(newMode);
  };

  // 对话模式处理函数
  const addDialogueLine = () => {
    // 防止快速连续添加
    if (isAddingDialogueLine) return

    setIsAddingDialogueLine(true)
    // 使用递增ID避免水合失败问题
    const newLineId = Math.max(...dialogueLines.map(line => line.id)) + 1
    const newLine = { id: newLineId, voice: voices[0]?.id || "", text: "" }
    setDialogueLines([...dialogueLines, newLine])

    // 自动激活新添加的对话行
    setActiveDialogueLineId(newLineId)

    // 使用 setTimeout 确保 DOM 更新后再执行滚动和聚焦
    setTimeout(() => {
      // 滚动到新行并聚焦输入框
      const newLineElement = document.querySelector(`[data-dialogue-line-id="${newLineId}"]`)
      if (newLineElement) {
        // 找到滚动容器（对话行的父容器）
        const scrollContainer = newLineElement.closest('.overflow-y-auto')

        if (scrollContainer) {
          // 在滚动容器内滚动到新行
          const containerRect = scrollContainer.getBoundingClientRect()
          const elementRect = newLineElement.getBoundingClientRect()
          const scrollTop = scrollContainer.scrollTop

          // 计算新行相对于容器的位置
          const relativeTop = elementRect.top - containerRect.top + scrollTop
          const containerHeight = containerRect.height
          const elementHeight = elementRect.height

          // 计算目标滚动位置（让新行在容器中居中）
          const targetScrollTop = relativeTop - (containerHeight / 2) + (elementHeight / 2)

          // 平滑滚动到目标位置
          scrollContainer.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth'
          })
        } else {
          // 如果找不到滚动容器，使用默认的页面滚动
          newLineElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          })
        }

        // 延迟聚焦到新行的输入框，确保滚动完成
        setTimeout(() => {
          const inputElement = newLineElement.querySelector('div[contenteditable="true"]') as HTMLElement
          if (inputElement) {
            inputElement.focus()
          }
          // 重置添加状态，允许下次添加
          setIsAddingDialogueLine(false)
        }, 300) // 额外延迟确保滚动动画完成
      } else {
        // 如果找不到新行元素，也要重置状态
        setIsAddingDialogueLine(false)
      }
    }, 100) // 100ms 延迟确保 DOM 完全更新
  }

  const removeDialogueLine = (idToRemove: number) => {
    if (dialogueLines.length > 1) { // 至少保留一行
      const filteredLines = dialogueLines.filter(line => line.id !== idToRemove)
      setDialogueLines(filteredLines)

      // 如果删除的是当前激活的行，自动激活剩余行中的第一行
      if (activeDialogueLineId === idToRemove && filteredLines.length > 0) {
        setActiveDialogueLineId(filteredLines[0].id)
      }

      // 如果删除的是当前编辑的行，清除编辑状态
      if (editingDialogueLineId === idToRemove) {
        setEditingDialogueLineId(null)
        setIsDropdownOpen(false)
      }
    }
  }

  const updateDialogueLine = (id: number, field: 'voice' | 'text', value: string) => {
    setDialogueLines(
      dialogueLines.map(line =>
        line.id === id ? { ...line, [field]: value } : line
      )
    )
  }

  // 批量导入对话处理函数
  const handleBatchImport = async (newLines: typeof dialogueLines, mode: 'replace' | 'append') => {
    try {
      if (mode === 'replace') {
        // 替换模式：清空现有对话，导入新对话
        setDialogueLines(newLines)
        // 激活第一个新导入的行，并同步主声音选择器
        if (newLines.length > 0) {
          // 先同步主声音选择器为第一行的声音，避免useEffect覆盖导入的声音
          setSelectedVoice(newLines[0].voice)
          setActiveDialogueLineId(newLines[0].id)
        }
      } else {
        // 追加模式：在现有对话后添加新对话
        const updatedLines = [...dialogueLines, ...newLines]
        setDialogueLines(updatedLines)
        // 激活第一个新导入的行，并同步主声音选择器
        if (newLines.length > 0) {
          // 先同步主声音选择器为第一行的声音，避免useEffect覆盖导入的声音
          setSelectedVoice(newLines[0].voice)
          setActiveDialogueLineId(newLines[0].id)

          // 滚动到新导入的第一行
          setTimeout(() => {
            const newLineElement = document.querySelector(`[data-dialogue-line-id="${newLines[0].id}"]`)
            if (newLineElement) {
              const scrollContainer = newLineElement.closest('.overflow-y-auto')
              if (scrollContainer) {
                newLineElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center'
                })
              }
            }
          }, 100)
        }
      }
    } catch (error) {
      console.error('批量导入失败:', error)
      throw error
    }
  }

  const handleLogout = useCallback(async () => {
    try {
      await auth.logout()
      window.location.href = "/login"
    } catch (error) {
      console.error("Logout error:", error)
      // 即使出错也跳转到登录页
      window.location.href = "/login"
    }
  }, [])

  // 🔧 性能优化：使用useCallback包裹ModelSelector的回调函数
  const handleModelChange = useCallback((modelId: string) => {
    setSelectedModel(modelId)
    setIsModelDropdownOpen(false)
  }, [])

  const handleToggleModelDropdown = useCallback(() => {
    setIsModelDropdownOpen(!isModelDropdownOpen)
  }, [isModelDropdownOpen])

  // 🔧 性能优化：使用useCallback包裹ParameterSliders的回调函数
  const handleStabilityChange = useCallback((value: number[]) => {
    setStability(value)
  }, [])

  const handleSimilarityChange = useCallback((value: number[]) => {
    setSimilarity(value)
  }, [])

  const handleStyleChange = useCallback((value: number[]) => {
    setStyle(value)
  }, [])

  const handleSpeechRateChange = useCallback((value: number[]) => {
    setSpeechRate(value)
  }, [])

  // 🔧 性能优化：使用useCallback包裹UserMenuButton的回调函数
  const handleToggleUserMenu = useCallback(() => {
    setShowUserMenu(!showUserMenu)
  }, [showUserMenu])

  const handleOpenChangePasswordDialog = useCallback(() => {
    setShowChangePasswordDialog(true)
    setShowUserMenu(false)
  }, [])

  // 🔧 性能优化：使用useCallback包裹SmartDialogueList的回调函数
  const handleUpdateDialogueText = useCallback((lineId: number, text: string) => {
    updateDialogueLine(lineId, 'text', text)
  }, [])

  // 🔧 性能优化：使用useCallback包裹VoiceSelectionList的回调函数
  const handleSelectVoice = useCallback((voiceId: string) => {
    setSelectedVoice(voiceId); // 更新主选择器的状态

    // 如果当前正在为某个对话行编辑声音，则更新该行的声音
    if (mode === 'dialogue' && editingDialogueLineId !== null) {
      updateDialogueLine(editingDialogueLineId, 'voice', voiceId);
    }

    setIsDropdownOpen(false); // 关闭下拉菜单
    setEditingDialogueLineId(null); // 清除编辑状态
  }, [mode, editingDialogueLineId])

  // 🔧 性能优化：使用useMemo包裹内联样式对象，避免每次重渲染都创建新对象
  const modeToggleShadowStyle = useMemo(() => ({
    boxShadow: mode === 'single'
      ? '0 4px 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2)'
      : '0 4px 20px rgba(147, 51, 234, 0.4), 0 2px 8px rgba(147, 51, 234, 0.2)'
  }), [mode])

  const progressBarStyle = useMemo(() => ({
    cursor: isDragging ? 'grabbing' : 'pointer'
  }), [isDragging])

  const progressFillStyle = useMemo(() => ({
    width: `${progress}%`,
    transition: isDragging ? 'none' : 'width 0.15s ease-out',
  }), [progress, isDragging])

  const progressThumbStyle = useMemo(() => ({
    left: `${progress}%`,
    marginLeft: '-6px',
    cursor: isDragging ? 'grabbing' : 'grab',
    transition: isDragging ? 'none' : undefined,
  }), [progress, isDragging])

  const staticStyles = useMemo(() => ({
    audioHidden: { display: 'none' },
    generateButtonTransparent: { background: 'transparent' },
    alertTriangleAnimation: { animationDuration: "2s" },
    sparklesAnimation: { animationDuration: "3s" }
  }), [])

  // 自动生成标签功能
  const handleAutoGenerateTags = async () => {
    // 检查是否有文本内容
    if (!text.trim()) {
      setTagGenerationError('请先输入要处理的文本')
      return
    }

    // 检查是否正在生成中
    if (isGeneratingTags) {
      return
    }

    // 基本权限检查：检查用户登录状态（后端会进行完整验证）
    if (!auth.isLoggedIn()) {
      setTagGenerationError('请先登录后使用自动标注功能')
      setShowAuthDialog(true)
      return
    }

    // 基本权限提示：检查会员状态（后端会进行完整验证）
    if (!userStatus.isVip) {
      setTagGenerationError('自动标注功能需要会员权限，请先开通会员')
      return
    }

    // 基本权限提示：检查会员是否过期（后端会进行完整验证）
    if (Date.now() > userStatus.expireAt) {
      setTagGenerationError('会员已过期，请续费后使用自动标注功能')
      return
    }

    setIsGeneratingTags(true)
    setTagGenerationError(null)

    try {
      // 使用统一的自动标注服务
      const result = await autoTagService.processText(text.trim(), 'auto');

      // 检查响应格式
      if (!result.success || !result.processedText) {
        throw new Error('服务返回的数据格式不正确')
      }

      // 将处理后的文本覆盖到输入框中
      setText(result.processedText)

      // 显示使用量信息（如果有）
      if (result.rateLimit) {
        console.log(`自动标注剩余次数: ${result.rateLimit.remaining}`)
      }

    } catch (error: any) {
      console.error('Auto generate tags error:', error)

      // 区分不同类型的错误
      let errorMessage = '自动生成标签失败，请稍后重试';

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络后重试';
      } else if (error.message.includes('登录')) {
        errorMessage = error.message;
        // 如果是登录相关错误，显示登录对话框
        setShowAuthDialog(true);
      } else if (error.message.includes('会员') || error.message.includes('权限')) {
        errorMessage = error.message;
      } else if (error.message.includes('频繁')) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTagGenerationError(errorMessage)
    } finally {
      setIsGeneratingTags(false)
    }
  }

  // 处理字符限制超出的函数
  const handleMaxLengthExceeded = useCallback((currentLength: number, maxLength: number) => {
    const message = `输入内容超过${maxLength}字符限制，原内容${currentLength}字符，已自动截取到${maxLength}字符`
    setCharLimitMessage(message)
    setShowCharLimitToast(true)

    // 3秒后自动隐藏提示
    setTimeout(() => {
      setShowCharLimitToast(false)
    }, 3000)
  }, [])

  // Guard Clauses: 只处理错误状态，移除加载状态显示
  if (voicesError) {
    return <VoicesError error={voicesError} onRetry={retryVoices} />;
  }

  // 修改密码处理函数
  const handleChangePassword = async () => {
    setChangePasswordError("")

    // 验证表单
    if (!changePasswordData.currentPassword || !changePasswordData.newPassword || !changePasswordData.confirmPassword) {
      setChangePasswordError("请填写所有密码字段")
      return
    }

    if (changePasswordData.newPassword !== changePasswordData.confirmPassword) {
      setChangePasswordError("新密码和确认密码不匹配")
      return
    }

    if (changePasswordData.newPassword.length < 6) {
      setChangePasswordError("新密码长度不能少于6位")
      return
    }

    if (changePasswordData.currentPassword === changePasswordData.newPassword) {
      setChangePasswordError("新密码不能与当前密码相同")
      return
    }

    setIsChangingPassword(true)

    try {
      await auth.changePassword({
        currentPassword: changePasswordData.currentPassword,
        newPassword: changePasswordData.newPassword
      })

      // 成功后关闭对话框并重置表单
      setShowChangePasswordDialog(false)
      setChangePasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      })
      setChangePasswordError("")

      // 显示成功提示对话框
      setShowPasswordSuccessDialog(true)

    } catch (error: any) {
      console.error('Change password error:', error)
      setChangePasswordError(error.message || '修改密码失败，请重试')
    } finally {
      setIsChangingPassword(false)
    }
  }

  // 【新增】数据分层保护 - 安全的数据设置函数（三层同步）
  const setTaskDataSafely = useCallback((taskData: any) => {
    // 第一层：React状态
    setOriginalTaskData(taskData)

    // 第二层：useRef备份
    originalTaskDataRef.current = taskData

    // 第三层：sessionStorage备份
    try {
      if (taskData) {
        sessionStorage.setItem(TASK_DATA_KEY, JSON.stringify(taskData))
      } else {
        sessionStorage.removeItem(TASK_DATA_KEY)
      }
    } catch (error) {
      console.error('[TASK-DATA-SAFE] Failed to save to sessionStorage:', error)
    }
  }, [TASK_DATA_KEY])

  // 【新增】数据分层保护 - 安全的数据获取函数（三层回退）
  const getTaskDataSafely = useCallback(() => {
    // 优先使用React状态
    if (originalTaskData) {
      return originalTaskData
    }

    // 回退到ref
    if (originalTaskDataRef.current) {
      // 恢复到React状态
      setOriginalTaskData(originalTaskDataRef.current)
      return originalTaskDataRef.current
    }

    // 最后回退到sessionStorage
    try {
      const stored = sessionStorage.getItem(TASK_DATA_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        // 恢复到前两层
        setOriginalTaskData(parsed)
        originalTaskDataRef.current = parsed
        return parsed
      }
    } catch (error) {
      console.error('[TASK-DATA-SAFE] Failed to recover from sessionStorage:', error)
    }

    return null
  }, [originalTaskData, TASK_DATA_KEY])

  // 【新增】原子性的重试状态更新函数
  const updateRetryState = useCallback((updater: (prev: typeof retryState) => typeof retryState) => {
    setRetryState(prev => {
      const newState = updater(prev)

      // 同步到sessionStorage进行持久化
      try {
        sessionStorage.setItem('retry_state', JSON.stringify({
          frontendAttempts: newState.frontendAttempts,
          datacenterAttempts: newState.datacenterAttempts,
          frontendStartTime: newState.frontendStartTime,
          errorHistory: newState.errorHistory.slice(-10) // 只保留最近10条
        }))
      } catch (error) {
        console.error('[RETRY-STATE] Failed to persist retry state:', error)
      }

      return newState
    })
  }, [])

  // 【新增】从sessionStorage恢复重试状态
  const recoverRetryState = useCallback(() => {
    try {
      const stored = sessionStorage.getItem('retry_state')
      if (stored) {
        const parsed = JSON.parse(stored)
        updateRetryState(prev => ({
          ...prev,
          frontendAttempts: parsed.frontendAttempts || 0,
          datacenterAttempts: parsed.datacenterAttempts || 0,
          frontendStartTime: parsed.frontendStartTime || 0,
          errorHistory: parsed.errorHistory || []
        }))
      }
    } catch (error) {
      console.error('[RETRY-STATE] Failed to recover retry state:', error)
    }
  }, [updateRetryState])

  // 【新增】从 sessionStorage 恢复任务映射的函数
  const getTaskMapping = useCallback((): TaskMapping | null => {
    try {
      const stored = sessionStorage.getItem(MAPPING_STORAGE_KEY);
      if (stored) {
        const mapping: TaskMapping = JSON.parse(stored);
        // 检查映射是否过期（例如，超过1小时）
        if (Date.now() - mapping.createdAt < 3600 * 1000) {
          return mapping;
        }
      }
      sessionStorage.removeItem(MAPPING_STORAGE_KEY);
      return null;
    } catch (e) {
      console.error('[TASK-MAPPING] Failed to get task mapping:', e);
      return null;
    }
  }, []);

  // 【新增】保存任务映射到 sessionStorage 的函数
  const saveTaskMapping = useCallback((mapping: TaskMapping) => {
    try {
      sessionStorage.setItem(MAPPING_STORAGE_KEY, JSON.stringify(mapping));
      console.log('[TASK-MAPPING] Saved mapping to sessionStorage:', mapping);
    } catch (e) {
      console.error('[TASK-MAPPING] Failed to save task mapping:', e);
    }
  }, []);

  // 【新增】页面加载时，尝试恢复进行中的任务状态
  useEffect(() => {
    const mapping = getTaskMapping();
    if (mapping) {
      console.log('[TASK-MAPPING] Recovered mapping from sessionStorage:', mapping);
      logicalTaskIdRef.current = mapping.logicalTaskId;
      setLogicalTaskId(mapping.logicalTaskId);
    }
  }, [getTaskMapping]);

  // 【新增】为TaskCenter提供的映射查询接口
  const getActualTaskIdForRefresh = useCallback((displayTaskId: string): string[] => {
    const mapping = getTaskMapping();
    if (mapping && mapping.displayTaskId === displayTaskId) {
      // 返回所有物理taskId，让TaskCenter尝试查询
      console.log('[TASK-MAPPING] Found mapping for refresh:', {
        displayTaskId,
        physicalTaskIds: mapping.physicalTaskIds
      });
      return mapping.physicalTaskIds;
    }

    // 如果没有找到映射，返回原始ID
    console.log('[TASK-MAPPING] No mapping found for refresh, using original ID:', displayTaskId);
    return [displayTaskId];
  }, [getTaskMapping]);

  // 【新增】页面加载时恢复重试状态
  useEffect(() => {
    recoverRetryState()
  }, [recoverRetryState])

  // 【优化】方案4：主界面状态监听 - 使用sessionStorage映射信息
  useEffect(() => {
    // 当主界面检测到音频生成完成时，同步更新任务中心中可能的相关任务
    if (hasAudio && audioUrls.downloadUrl && taskStatus === 'complete') {
      console.log('[STATUS-SYNC] Detected audio generation completed, syncing task center status')

      // 从sessionStorage获取当前映射信息
      const mapping = getTaskMapping()
      if (mapping && taskCenterRef.current) {
        // 使用映射信息更新任务中心
        taskCenterRef.current.updateTaskStatus(mapping.displayTaskId, 'complete', audioUrls.downloadUrl)
        console.log('[STATUS-SYNC] Updated task center via mapping:', {
          displayTaskId: mapping.displayTaskId,
          logicalTaskId: mapping.logicalTaskId,
          downloadUrl: audioUrls.downloadUrl
        })
      }
    }
  }, [hasAudio, audioUrls.downloadUrl, taskStatus, getTaskMapping])

  // 【新增】结构化错误响应接口
  interface StructuredErrorResponse {
    type: 'error';
    message: string;
    errorType?: string;
    isRetryable?: boolean;
  }

  // 【新增】错误类型映射表
  const ERROR_TYPE_MESSAGES = {
    'content_violation': '我们深表歉意，您使用的文本可能违反了Elevenlabs的服务条款，因此已被屏蔽。(此信息由Elevenlabs官方返回)',
    'quota_exceeded': '您的配额已用完，请充值后继续使用。',
    'authentication_failed': '登录会话已过期，请重新登录。',
    'rate_limit_exceeded': '请求过于频繁，请稍后再试。',
    'system_error': '系统暂时繁忙，请稍后再试。',
    'permission_denied': '您没有执行此操作的权限。',
    'invalid_input': '输入参数无效，请检查后重试。',
    'proxy_failover_failure': '主备服务器均不可用，请稍后再试或联系客服。'
  } as const;

  // 【新增】解析错误响应的函数
  const parseErrorResponse = useCallback((data: any): {
    message: string;
    errorType: string | null;
    isRetryable: boolean;
    isStructured: boolean;
  } => {
    // 检查是否为新的结构化错误响应
    if (data.errorType !== undefined && data.isRetryable !== undefined) {
      return {
        message: data.message || '',
        errorType: data.errorType || null,
        isRetryable: data.isRetryable || false,
        isStructured: true
      };
    }

    // 兼容旧的错误格式
    return {
      message: data.message || '',
      errorType: null,
      isRetryable: false,
      isStructured: false
    };
  }, []);

  // 【新增】获取用户友好的错误消息
  const getUserFriendlyErrorMessage = useCallback((errorType: string | null, originalMessage: string): string => {
    if (errorType && ERROR_TYPE_MESSAGES[errorType as keyof typeof ERROR_TYPE_MESSAGES]) {
      return ERROR_TYPE_MESSAGES[errorType as keyof typeof ERROR_TYPE_MESSAGES];
    }
    return originalMessage;
  }, []);

  // 【扩展】错误分类函数 - 支持结构化错误响应
  const isSystemTemporaryError = useCallback((errorMessage: string, errorType?: string | null, isRetryable?: boolean): boolean => {
    // 优先使用结构化错误信息
    if (errorType !== undefined && isRetryable !== undefined) {
      return isRetryable === true;
    }

    // 兼容原有的关键词检测
    const temporaryErrorKeywords = [
      'too many chunks failed',
      'chunks failed even after retry',
      'system issue',
      'temporary failure',
      'processing timeout',
      'internal server error',
      'service temporarily unavailable',
      'connection reset',
      'request timeout',
      'network error',
      'service unavailable'
    ];

    const lowerMessage = errorMessage.toLowerCase();
    return temporaryErrorKeywords.some(keyword => lowerMessage.includes(keyword));
  }, []);

  const isUserRelatedError = useCallback((errorMessage: string, errorType?: string | null): boolean => {
    // 优先使用结构化错误信息
    if (errorType) {
      const userRelatedTypes = [
        'content_violation',
        'quota_exceeded',
        'authentication_failed',
        'permission_denied',
        'invalid_input',
        'proxy_failover_failure'  // 【新增】代理故障转移失败也是用户相关错误（不可重试）
      ];
      return userRelatedTypes.includes(errorType);
    }

    // 兼容原有的关键词检测
    const userErrorKeywords = [
      '会员权限不足',
      '配额不足',
      '登录会话已过期',
      '参数验证失败',
      '文本内容违规',
      '非会员用户',
      'token',
      '登录',
      '权限',
      '配额',
      '会员'
    ];

    const lowerMessage = errorMessage.toLowerCase();
    return userErrorKeywords.some(keyword => lowerMessage.includes(keyword));
  }, []);

  // 【新增】逻辑任务管理函数 - 解决重试时任务重复创建问题
  const generateLogicalTaskId = useCallback((): string => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `logical_${timestamp}_${random}`
  }, []);

  // 【保留】原有的逻辑任务状态更新函数作为备用
  const updateLogicalTaskStatus = useCallback((status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => {
    // 【修复】使用真实的物理任务ID更新任务中心状态
    const currentTaskId = currentTaskIdRef.current
    if (currentTaskId && taskCenterRef.current) {
      taskCenterRef.current.updateTaskStatus(currentTaskId, status, downloadUrl)
      console.log('[LOGICAL-TASK] Updated task status:', currentTaskId, 'status:', status)
    }
  }, []);

  const resetLogicalTaskState = useCallback(() => {
    setLogicalTaskId(null)
    logicalTaskIdRef.current = null
    setPhysicalTaskIds([])

    // 【优化】清理sessionStorage中的任务映射
    try {
      sessionStorage.removeItem(MAPPING_STORAGE_KEY)
      sessionStorage.removeItem('current_logical_task_id')
      sessionStorage.removeItem('logical_task_timestamp')
    } catch (error) {
      console.warn('[TASK-MAPPING] Failed to clear sessionStorage:', error)
    }

    console.log('[TASK-MAPPING] Cleared task mapping and logical task state')
  }, []);

  // 【新增】页面刷新恢复逻辑任务状态
  useEffect(() => {
    try {
      const savedLogicalTaskId = sessionStorage.getItem('current_logical_task_id')
      const savedTimestamp = sessionStorage.getItem('logical_task_timestamp')

      if (savedLogicalTaskId && savedTimestamp) {
        const timestamp = parseInt(savedTimestamp)
        const now = Date.now()

        // 如果任务创建时间超过1小时，认为已过期
        if (now - timestamp < 60 * 60 * 1000) {
          setLogicalTaskId(savedLogicalTaskId)
          logicalTaskIdRef.current = savedLogicalTaskId
          // 注意：不再需要设置 isInitialRequest，因为我们现在完全依赖 ref 进行去重判断
          console.log('[LOGICAL-TASK] Recovered logical task:', savedLogicalTaskId)
        } else {
          // 清理过期的任务
          sessionStorage.removeItem('current_logical_task_id')
          sessionStorage.removeItem('logical_task_timestamp')
        }
      }
    } catch (error) {
      console.warn('[LOGICAL-TASK] Failed to recover from sessionStorage:', error)
    }
  }, []);

  // 【增强】构建 WebSocket URL 的辅助函数，支持多个备用API切换
  const buildWsUrl = useCallback((currentMode: 'single' | 'dialogue', excludeLocationsParam: string = '', backupIndex: number = -1): string => {
    const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";

    // 【增强】根据backupIndex参数选择API URL
    let baseApiUrl: string | undefined;

    if (backupIndex >= 0 && process.env.NEXT_PUBLIC_ENABLE_BACKUP_API === 'true' && process.env.NEXT_PUBLIC_BACKUP_API_URL) {
      // 解析多个备用API地址
      const backupUrls = process.env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0);

      if (backupIndex < backupUrls.length) {
        baseApiUrl = backupUrls[backupIndex];
        console.log(`[BACKUP-API] Using backup API ${backupIndex + 1}/${backupUrls.length}:`, baseApiUrl);
      } else {
        console.warn(`[BACKUP-API] Invalid backup index ${backupIndex}, max available: ${backupUrls.length - 1}`);
        baseApiUrl = process.env.NEXT_PUBLIC_API_URL;
      }
    } else {
      baseApiUrl = process.env.NEXT_PUBLIC_API_URL;
    }

    const wsHost = baseApiUrl
        ? new URL(baseApiUrl).host
        : window.location.host;

    // 【关键修复】根据模式选择正确的端点
    const endpoint = currentMode === 'dialogue' ? '/api/tts/ws/dialogue/generate' : '/api/tts/ws/generate';

    // 【修复】使用正确的环境变量
    const apiUrlForWs = baseApiUrl?.replace('https://', 'wss://').replace('http://', 'ws://');
    if (!apiUrlForWs) {
        console.error("API URL is not configured!");
        // 回退到当前域名
        return `${wsProtocol}//${wsHost}${endpoint}${excludeLocationsParam}`;
    }

    return `${apiUrlForWs}${endpoint}${excludeLocationsParam}`;
  }, []);

  // 【新增】获取备用API数量的辅助函数
  const getBackupApiCount = useCallback((): number => {
    if (process.env.NEXT_PUBLIC_ENABLE_BACKUP_API !== 'true' || !process.env.NEXT_PUBLIC_BACKUP_API_URL) {
      return 0;
    }

    const backupUrls = process.env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0);
    return backupUrls.length;
  }, []);

  // 【新增】初始化备用API最大索引
  const initializeBackupApiState = useCallback(() => {
    const backupCount = getBackupApiCount();
    updateRetryState(prev => ({
      ...prev,
      maxBackupIndex: backupCount > 0 ? backupCount - 1 : -1
    }));
  }, [getBackupApiCount]);

  // 【新增】构建请求参数的辅助函数
  const buildRequestParams = useCallback((taskData: any) => {
    const token = TokenManager.getAccessToken();

    let requestParams: any = {
      action: 'start',
      token: token,
      model: taskData.model,
    };

    if (taskData.taskType === 'dialogue') {
      requestParams.taskType = 'dialogue';
      requestParams.dialogue = taskData.dialogueLines;
    } else {
      requestParams.input = taskData.input;
      requestParams.voice = taskData.voice;
    }

    // 添加其他参数
    requestParams.stability = taskData.stability;
    requestParams.similarity_boost = taskData.similarity_boost;
    requestParams.speed = taskData.speed;

    if (taskData.model !== "eleven_turbo_v2" && taskData.model !== "eleven_turbo_v2_5") {
      requestParams.style = taskData.style;
    }

    return requestParams;
  }, []);



  // 【新增】增强的前端重试函数（使用新的状态管理）
  const retryWithFrontendLogic = useCallback(async (errorMessage: string) => {
    // 1. 状态检查和锁定
    if (retryState.isLocked && retryState.activeRetryType !== 'frontend') {
      return
    }

    // 2. 原子性地检查和更新重试次数
    let shouldProceed = false
    let currentAttempt = 0

    updateRetryState(prev => {
      const nextAttempt = prev.frontendAttempts + 1

      if (nextAttempt > prev.frontendMaxAttempts) {
        return {
          ...prev,
          frontendInProgress: false,
          isLocked: false,
          activeRetryType: null
        }
      }

      shouldProceed = true
      currentAttempt = nextAttempt

      return {
        ...prev,
        frontendAttempts: nextAttempt,
        frontendInProgress: true,
        isLocked: true,
        lockReason: `frontend_retry_attempt_${nextAttempt}`,
        activeRetryType: 'frontend',
        frontendStartTime: prev.frontendStartTime || Date.now(),
        errorHistory: [
          ...prev.errorHistory,
          {
            timestamp: Date.now(),
            type: 'system_temp',
            message: errorMessage,
            retryType: 'frontend'
          }
        ]
      }
    })

    // 3. 如果超过最大重试次数，执行失败处理
    if (!shouldProceed) {
      setIsGenerating(false)
      setTaskStatus('failed')
      setTaskProgress('多次重试失败')
      setError('系统暂时繁忙，请稍后再试或联系客服')

      // 清理重试状态
      updateRetryState(prev => ({
        ...prev,
        frontendInProgress: false,
        isLocked: false,
        activeRetryType: null
      }))
      return
    }

    // 4. 执行重试逻辑
    try {
      setTaskProgress(`正在重试... (${currentAttempt}/${retryState.frontendMaxAttempts})`)

      // 延迟重试，给系统恢复时间
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 使用安全的数据获取方法
      const taskData = getTaskDataSafely()
      if (!taskData) {
        throw new Error('原始任务数据丢失')
      }

      // 构建WebSocket URL
      const retryMode = taskData.taskType === 'dialogue' ? 'dialogue' : 'single'
      const wsUrl = buildWsUrl(retryMode)

      const ws = new WebSocket(wsUrl)

      // 设置WebSocket事件处理器，带有重试上下文
      setupWebSocketHandlersWithRetryContext(ws, {
        retryType: 'frontend',
        attemptNumber: currentAttempt,
        maxAttempts: retryState.frontendMaxAttempts
      })

      ws.onopen = () => {
        const retryParams = buildRequestParams(taskData)
        ws.send(JSON.stringify(retryParams))
      }

    } catch (error) {
      console.error('[FRONTEND-RETRY] Failed to create retry connection:', error)
      setIsGenerating(false)
      setTaskStatus('failed')
      setError('重试连接失败，请稍后再试')

      // 解锁状态
      updateRetryState(prev => ({
        ...prev,
        frontendInProgress: false,
        isLocked: false,
        activeRetryType: null
      }))
    }
  }, [retryState, updateRetryState, getTaskDataSafely, buildWsUrl, buildRequestParams]);

  // 【增强】备用API重试函数 - 支持多个备用API轮询
  const retryWithBackupApi = useCallback(async (errorMessage: string) => {
    // 1. 检查备用API是否可用
    if (!process.env.NEXT_PUBLIC_ENABLE_BACKUP_API || !process.env.NEXT_PUBLIC_BACKUP_API_URL) {
      console.log('[BACKUP-API] Backup API not configured, skipping backup retry');
      setIsGenerating(false)
      setTaskStatus('failed')
      setTaskProgress('重试失败')
      setError('系统暂时繁忙，请稍后再试或联系客服')
      return
    }

    // 2. 解析备用API列表
    const backupUrls = process.env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0);
    if (backupUrls.length === 0) {
      console.log('[BACKUP-API] No valid backup APIs found');
      setIsGenerating(false)
      setTaskStatus('failed')
      setTaskProgress('重试失败')
      setError('系统暂时繁忙，请稍后再试或联系客服')
      return
    }

    // 3. 状态检查和锁定
    if (retryState.isLocked && retryState.activeRetryType !== 'backup') {
      return
    }

    // 4. 确定下一个要尝试的备用API索引
    let shouldProceed = false
    let currentBackupIndex = -1
    let nextBackupIndex = retryState.currentBackupIndex + 1

    // 如果已经尝试完所有备用API，则失败
    if (nextBackupIndex >= backupUrls.length) {
      console.log(`[BACKUP-API] All backup APIs exhausted (${backupUrls.length} tried)`);
      setIsGenerating(false)
      setTaskStatus('failed')
      setTaskProgress('所有备用服务器均不可用')
      setError('主备服务器均不可用，请稍后再试或联系客服')

      // 重置状态
      updateRetryState(prev => ({
        ...prev,
        usingBackupApi: false,
        currentBackupIndex: -1,
        isLocked: false,
        activeRetryType: null
      }))
      return
    }

    // 5. 更新状态，准备使用下一个备用API
    updateRetryState(prev => {
      shouldProceed = true
      currentBackupIndex = nextBackupIndex

      return {
        ...prev,
        currentBackupIndex: nextBackupIndex,
        usingBackupApi: true,
        isLocked: true,
        lockReason: `backup_api_${nextBackupIndex}_retry`,
        activeRetryType: 'backup',
        backupApiStartTime: prev.backupApiStartTime || Date.now(),
        errorHistory: [
          ...prev.errorHistory,
          {
            timestamp: Date.now(),
            type: `backup_api_${nextBackupIndex}`,
            message: errorMessage,
            retryType: 'backup'
          }
        ]
      }
    })

    // 6. 如果不应该继续，直接返回（前面已经处理了失败情况）
    if (!shouldProceed) {
      return
    }

    // 7. 执行备用API重试逻辑
    try {
      const currentApiUrl = backupUrls[currentBackupIndex];
      setTaskProgress(`正在尝试备用服务器 ${currentBackupIndex + 1}/${backupUrls.length}...`)

      // 延迟重试，给系统恢复时间
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 使用安全的数据获取方法
      const taskData = getTaskDataSafely()
      if (!taskData) {
        throw new Error('原始任务数据丢失')
      }

      // 构建备用API的WebSocket URL
      const retryMode = taskData.taskType === 'dialogue' ? 'dialogue' : 'single'
      const wsUrl = buildWsUrl(retryMode, '', currentBackupIndex) // 使用指定索引的备用API

      console.log(`[BACKUP-API] Attempting backup API ${currentBackupIndex + 1}/${backupUrls.length}: ${currentApiUrl}`)
      const ws = new WebSocket(wsUrl)

      // 设置WebSocket事件处理器，带有备用API重试上下文
      setupWebSocketHandlersWithRetryContext(ws, {
        retryType: 'backup',
        attemptNumber: currentBackupIndex + 1,
        maxAttempts: backupUrls.length
      })

      ws.onopen = () => {
        const retryParams = buildRequestParams(taskData)
        ws.send(JSON.stringify(retryParams))
      }

    } catch (error) {
      console.error(`[BACKUP-API-RETRY] Failed to create backup API connection (index ${currentBackupIndex}):`, error)

      // 如果还有其他备用API可以尝试，继续下一个
      if (currentBackupIndex + 1 < backupUrls.length) {
        console.log(`[BACKUP-API] Trying next backup API...`)
        setTimeout(() => {
          retryWithBackupApi(errorMessage)
        }, 1000)
      } else {
        // 所有备用API都失败了
        setIsGenerating(false)
        setTaskStatus('failed')
        setError('所有备用API连接失败，请稍后再试')

        // 重置状态
        updateRetryState(prev => ({
          ...prev,
          usingBackupApi: false,
          currentBackupIndex: -1,
          isLocked: false,
          activeRetryType: null
        }))
      }
    }
  }, [retryState, updateRetryState, getTaskDataSafely, buildWsUrl, buildRequestParams]);

  // 【修复】数据中心切换重试函数
  const retryWithDatacenterSwitch = async (excludeLocations: string[], taskData: any) => {
    const currentRetryCount = retryCount + 1

    if (currentRetryCount > MAX_RETRY_ATTEMPTS) {
      setIsGenerating(false)
      setTaskStatus('failed')
      setTaskProgress('多次重试失败')
      setError('检查网络环境，请稍后再试')
      return
    }

    setRetryCount(currentRetryCount)
    setExcludedLocations(prev => [...prev, ...excludeLocations])
    setTaskProgress('正在重试...')

    try {
      // 构建排除位置的查询参数
      const newExcludedLocations = [...excludedLocations, ...excludeLocations]
      const excludeParam = newExcludedLocations.length > 0
        ? `?excludeLocations=${newExcludedLocations.join(',')}`
        : ''

      // 【关键修复】从 taskData 中获取正确的模式
      const retryMode = (taskData?.taskType === 'dialogue') ? 'dialogue' : 'single';

      // 【修复】使用辅助函数构建正确的 URL
      const wsUrl = buildWsUrl(retryMode, excludeParam);

      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        // 发送重试请求
        const retryParams = {
          action: 'retry',
          recoveryData: taskData || originalTaskData,
          retryCount: currentRetryCount,
          excludedLocations: newExcludedLocations
        }

        ws.send(JSON.stringify(retryParams))
      }

      // 使用新的、带上下文的WebSocket处理器
      setupWebSocketHandlersWithRetryContext(ws, {
        retryType: 'datacenter',
        attemptNumber: currentRetryCount,
        maxAttempts: MAX_RETRY_ATTEMPTS
      })

    } catch (error) {
      console.error('[RETRY] Failed to create retry connection:', error)
      setIsGenerating(false)
      setTaskStatus('failed')
      setError('重试连接失败，请稍后再试')
    }
  }

  // 【新增】带重试上下文的WebSocket处理器
  const setupWebSocketHandlersWithRetryContext = useCallback((
    ws: WebSocket,
    retryContext?: {
      retryType: 'frontend' | 'datacenter' | 'backup',
      attemptNumber: number,
      maxAttempts: number
    }
  ) => {
    const isRetryConnection = !!retryContext

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)

        switch (data.type) {
          case 'initialized':
            // 【优化】处理初始化消息，使用sessionStorage映射机制
            if (data.taskId) {
              const physicalId = data.taskId
              setCurrentTaskId(physicalId)
              currentTaskIdRef.current = physicalId

              // 记录物理任务ID
              setPhysicalTaskIds(prev => {
                if (!prev.includes(physicalId)) {
                  return [...prev, physicalId]
                }
                return prev
              })

              const currentLogicalId = logicalTaskIdRef.current
              if (currentLogicalId) {
                let mapping = getTaskMapping()

                if (!mapping || mapping.logicalTaskId !== currentLogicalId) {
                  // 这是新请求的第一个物理ID
                  mapping = {
                    logicalTaskId: currentLogicalId,
                    displayTaskId: physicalId, // 使用第一个物理ID作为显示ID
                    physicalTaskIds: [physicalId],
                    createdAt: Date.now()
                  }

                  // 【核心】向任务中心添加任务，使用 displayTaskId
                  if (taskCenterRef.current) {
                    taskCenterRef.current.addTask(mapping.displayTaskId)
                  }
                  console.log('[TASK-MAPPING] New task created:', mapping)
                } else {
                  // 这是重试产生的ID
                  if (!mapping.physicalTaskIds.includes(physicalId)) {
                    mapping.physicalTaskIds.push(physicalId)
                    console.log('[TASK-MAPPING] Added physical task to mapping:', mapping)
                  }
                }
                saveTaskMapping(mapping)
              }
            }
            setTaskStatus('processing')
            setTaskProgress('连接成功，任务准备就绪...')
            break

          case 'progress':
            // 【新增】处理进度消息，更新任务进度显示
            if (data.message) {
              setTaskProgress(data.message)
            }
            break

          case 'heartbeat':
            // 【新增】处理心跳消息，保持连接活跃
            console.log('[WebSocket] Heartbeat received', data.timestamp)
            break

          case 'progress_update':
            // 【新增】处理进度更新消息
            if (data.message) {
              setTaskProgress(data.message)
            }
            console.log('[WebSocket] Progress update:', data.message)
            break

          case 'complete':
            // 【修复】确保任务完成后按钮状态正确重置
            setIsGenerating(false)
            setTaskStatus('complete')
            setTaskProgress('音频生成完成！')
            setHasAudio(true)
            setAudioUrls({
              streamUrl: data.streamUrl || null,
              downloadUrl: data.downloadUrl || null,
              secureStreamUrl: null // 将在后续异步加载
            })

            // 异步加载安全的音频
            if (data.streamUrl) {
              loadSecureAudio(data.streamUrl).then(secureUrl => {
                if (secureUrl) {
                  setAudioUrls(prev => ({
                    ...prev,
                    secureStreamUrl: secureUrl
                  }))

                  if (audioRef.current) {
                    audioRef.current.src = secureUrl
                  }

                  console.log('[SECURE-AUDIO] Audio loaded and ready for playback')
                } else {
                  setError('音频加载失败，请重试')
                }
              })
            }

            // 【优化】使用sessionStorage映射关系更新任务状态
            const completedPhysicalId = data.taskId || currentTaskIdRef.current
            if (completedPhysicalId) {
              const mapping = getTaskMapping()
              if (mapping && mapping.physicalTaskIds.includes(completedPhysicalId)) {
                // 【核心】使用映射中的 displayTaskId 来更新任务中心
                if (taskCenterRef.current) {
                  taskCenterRef.current.updateTaskStatus(mapping.displayTaskId, 'complete', data.downloadUrl)
                }
                console.log(`[TASK-MAPPING] Task complete. Updating display task '${mapping.displayTaskId}' for physical task '${completedPhysicalId}'.`)
              } else {
                // 回退逻辑：如果找不到映射，直接尝试更新
                if (taskCenterRef.current) {
                  taskCenterRef.current.updateTaskStatus(completedPhysicalId, 'complete', data.downloadUrl)
                }
                console.warn(`[TASK-MAPPING] Could not find mapping for completed task '${completedPhysicalId}'. Updating directly.`)
              }
            }

            // 【保留】原有的逻辑任务状态更新作为备用
            updateLogicalTaskStatus('complete', data.downloadUrl)

            // 成功完成，重置所有重试状态
            updateRetryState(prev => ({
              ...prev,
              frontendAttempts: 0,
              datacenterAttempts: 0,
              backupApiAttempts: 0,
              frontendInProgress: false,
              usingBackupApi: false,
              currentBackupIndex: -1,
              isLocked: false,
              activeRetryType: null,
              frontendStartTime: 0,
              backupApiStartTime: 0,
              errorHistory: []
            }))

            // 清理任务数据
            setTaskDataSafely(null)

            // 【优化】任务成功，清理映射状态
            resetLogicalTaskState()
            resetLogicalTaskState()

            // 【修复】移除直接设置audioRef.current.src的逻辑，避免与安全音频加载冲突
            // 音频源的设置已经在上面的loadSecureAudio异步逻辑中处理
            ws.close()
            break

          case 'error':
            // 【增强】解析错误响应，支持结构化错误格式
            const errorInfo = parseErrorResponse(data);
            const userFriendlyMessage = getSafeUserFriendlyMessage({
              message: errorInfo.message,
              errorType: errorInfo.errorType,
              code: errorInfo.errorType
            });

            console.error('[WebSocket] Generation Error:', {
              original: errorInfo.message,
              errorType: errorInfo.errorType,
              isRetryable: errorInfo.isRetryable,
              isStructured: errorInfo.isStructured,
              userFriendly: userFriendlyMessage
            });

            // 如果这是重试连接的错误，需要特殊处理
            if (isRetryConnection) {
              // 检查是否还能继续重试
              if (retryContext.retryType === 'frontend') {
                if (isSystemTemporaryError(errorInfo.message, errorInfo.errorType, errorInfo.isRetryable) &&
                    retryContext.attemptNumber < retryContext.maxAttempts) {
                  // 可以继续前端重试
                  ws.close()
                  setTimeout(() => {
                    retryWithFrontendLogic(errorInfo.message)
                  }, 1000)
                  return
                } else {
                  // 【增强】前端重试用完，检查是否可以尝试备用API
                  if (process.env.NEXT_PUBLIC_ENABLE_BACKUP_API === 'true' &&
                      process.env.NEXT_PUBLIC_BACKUP_API_URL &&
                      !retryState.usingBackupApi &&
                      retryState.currentBackupIndex === -1) {

                    console.log('[RETRY-STRATEGY] Frontend retries exhausted, trying backup API');
                    ws.close()
                    setTimeout(() => {
                      retryWithBackupApi(errorInfo.message)
                    }, 1000)
                    return
                  }

                  // 前端重试和备用API都用完，最终失败
                  setIsGenerating(false)
                  setTaskStatus('failed')
                  setTaskProgress('多次重试失败')
                  setError('系统暂时繁忙，请稍后再试或联系客服')

                  // 【优化】使用映射关系更新任务状态为失败
                  const mapping = getTaskMapping()
                  if (mapping && taskCenterRef.current) {
                    taskCenterRef.current.updateTaskStatus(mapping.displayTaskId, 'failed')
                    console.log('[TASK-MAPPING] Frontend retry failed, updated display task:', mapping.displayTaskId)
                  }

                  // 【保留】原有的逻辑任务状态更新作为备用
                  updateLogicalTaskStatus('failed')

                  // 重置重试状态
                  updateRetryState(prev => ({
                    ...prev,
                    frontendInProgress: false,
                    usingBackupApi: false,
                    isLocked: false,
                    activeRetryType: null
                  }))

                  // 【优化】最终失败后清理映射状态
                  resetLogicalTaskState()

                  ws.close()
                  return
                }
              } else if (retryContext.retryType === 'backup') {
                // 【增强】备用API重试的错误处理 - 支持多个备用API
                const backupUrls = process.env.NEXT_PUBLIC_BACKUP_API_URL?.split(',').map(url => url.trim()).filter(url => url.length > 0) || [];
                const hasMoreBackupApis = retryState.currentBackupIndex + 1 < backupUrls.length;

                if (isSystemTemporaryError(errorInfo.message, errorInfo.errorType, errorInfo.isRetryable) && hasMoreBackupApis) {
                  // 还有其他备用API可以尝试
                  console.log(`[BACKUP-API] Current backup API failed, trying next one (${retryState.currentBackupIndex + 2}/${backupUrls.length})`);
                  ws.close()
                  setTimeout(() => {
                    retryWithBackupApi(errorInfo.message)
                  }, 1000)
                  return
                } else {
                  // 所有备用API都用完，最终失败
                  setIsGenerating(false)
                  setTaskStatus('failed')
                  setTaskProgress('所有备用服务器均不可用')
                  setError('主备服务器均不可用，请稍后再试或联系客服')

                  // 【优化】使用映射关系更新任务状态为失败
                  const mapping = getTaskMapping()
                  if (mapping && taskCenterRef.current) {
                    taskCenterRef.current.updateTaskStatus(mapping.displayTaskId, 'failed')
                    console.log('[TASK-MAPPING] All backup APIs failed, updated display task:', mapping.displayTaskId)
                  }

                  // 【保留】原有的逻辑任务状态更新作为备用
                  updateLogicalTaskStatus('failed')

                  // 重置重试状态
                  updateRetryState(prev => ({
                    ...prev,
                    usingBackupApi: false,
                    currentBackupIndex: -1,
                    isLocked: false,
                    activeRetryType: null
                  }))

                  // 【优化】最终失败后清理映射状态
                  resetLogicalTaskState()

                  ws.close()
                  return
                }
              }
            }

            // 非重试连接的错误处理（增强支持结构化错误）
            if (isUserRelatedError(errorInfo.message, errorInfo.errorType)) {
              // 用户相关错误，直接失败
              setIsGenerating(false)
              setTaskStatus('failed')
              setTaskProgress('生成失败')
              autoplayNextRef.current = false

              // 【增强】更新逻辑任务状态为失败
              updateLogicalTaskStatus('failed')
              console.log('[LOGICAL-TASK] User related error, logical task:', logicalTaskId || logicalTaskIdRef.current)

              // 重置重试状态
              updateRetryState(prev => ({
                ...prev,
                frontendAttempts: 0,
                datacenterAttempts: 0,
                frontendInProgress: false,
                isLocked: false,
                activeRetryType: null,
                frontendStartTime: 0
              }))

              setTaskDataSafely(null)

              // 【新增】重置逻辑任务状态
              resetLogicalTaskState()

              // 【增强】显示用户友好的错误消息
              setError(userFriendlyMessage);

              // 根据错误类型显示相应的对话框
              if (errorInfo.errorType === 'authentication_failed' ||
                  errorInfo.message.toLowerCase().includes('token') ||
                  errorInfo.message.toLowerCase().includes('登录')) {
                setShowAuthDialog(true)
              } else if (errorInfo.errorType === 'quota_exceeded' ||
                         errorInfo.message.toLowerCase().includes('会员') ||
                         errorInfo.message.toLowerCase().includes('quota')) {
                setShowVipDialog(true)
              }
            } else if (isSystemTemporaryError(errorInfo.message, errorInfo.errorType, errorInfo.isRetryable) && !retryState.frontendInProgress) {
              // 【增强】系统临时性错误，且当前没有在重试
              if (retryState.frontendAttempts < retryState.frontendMaxAttempts) {
                // 还可以进行前端重试
                ws.close()
                setTimeout(() => {
                  retryWithFrontendLogic(errorInfo.message)
                }, 1000)
              } else if (process.env.NEXT_PUBLIC_ENABLE_BACKUP_API === 'true' &&
                         process.env.NEXT_PUBLIC_BACKUP_API_URL &&
                         !retryState.usingBackupApi &&
                         retryState.currentBackupIndex === -1) {
                // 前端重试用完，尝试备用API
                console.log('[RETRY-STRATEGY] Frontend retries exhausted, trying backup API for system error');
                ws.close()
                setTimeout(() => {
                  retryWithBackupApi(errorInfo.message)
                }, 1000)
              } else {
                // 所有重试都用完，失败
                setIsGenerating(false)
                setTaskStatus('failed')
                setTaskProgress('系统重试失败')
                setError('系统暂时繁忙，请稍后再试或联系客服')

                // 【新增】更新逻辑任务状态为失败
                updateLogicalTaskStatus('failed')
                console.log('[LOGICAL-TASK] System retry failed, logical task:', logicalTaskId || logicalTaskIdRef.current)
              }
            } else {
              // 其他错误，直接失败
              setIsGenerating(false)
              setTaskStatus('failed')
              setTaskProgress('生成失败')
              autoplayNextRef.current = false

              // 【增强】更新逻辑任务状态为失败
              updateLogicalTaskStatus('failed')
              console.log('[LOGICAL-TASK] Other error, logical task:', logicalTaskId || logicalTaskIdRef.current)

              // 重置重试状态
              updateRetryState(prev => ({
                ...prev,
                frontendInProgress: false,
                isLocked: false,
                activeRetryType: null
              }))

              // 【新增】重置逻辑任务状态
              resetLogicalTaskState()

              // 【增强】显示用户友好的错误消息
              setError(userFriendlyMessage || '音频生成失败，请稍后再试！【可尝试切换“梯子”地区】')
            }
            break

          case 'error_retryable':
            // 数据中心级错误，使用现有机制
            setTaskProgress('正在重试...')
            ws.close()
            setTimeout(() => {
              retryWithDatacenterSwitch(data.excludeLocations || [], data.taskData)
            }, 1000)
            break

          default:
            // 其他消息类型，使用原有的处理逻辑
            console.warn('[WebSocket] Unknown message type:', data.type)
        }
      } catch (error) {
        console.error('[WebSocket] Failed to parse message:', error)
        setError('收到无法解析的数据')
      }
    }

    // 【增强】防护型的关闭处理
    ws.onclose = (event) => {
      console.log('[WebSocket] Connection closed', {
        isRetryConnection,
        retryContext,
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        taskStatus,
        isGenerating
      })

      // 【关键修复】检测异常关闭情况
      const isAbnormalClose = !event.wasClean || event.code !== 1000
      const isTaskIncomplete = taskStatus !== 'complete' && taskStatus !== 'failed'

      // 【修复】在以下情况下重置生成状态：
      // 1. 非重试连接
      // 2. 重试失败（达到最大重试次数）
      // 3. 任务已完成（防止备用API成功后按钮状态异常）
      // 4. 【新增】异常关闭且任务未完成（防止状态卡住）
      if (!isRetryConnection ||
          (retryContext && retryContext.attemptNumber >= retryContext.maxAttempts) ||
          taskStatus === 'complete' ||
          (isAbnormalClose && isTaskIncomplete)) {

        // 【修复】确保生成状态正确重置
        if (isGenerating) {
          setIsGenerating(false)
          console.log('[WebSocket] Reset isGenerating on close', {
            reason: isAbnormalClose && isTaskIncomplete ? 'abnormal_close' : 'normal_close',
            code: event.code,
            wasClean: event.wasClean
          })
        }

        // 【新增】如果是异常关闭且任务未完成，设置错误状态
        if (isAbnormalClose && isTaskIncomplete && !error) {
          setError('连接意外中断，请重试')
          setTaskStatus('failed')
          setTaskProgress('连接中断')
          console.log('[WebSocket] Set error due to abnormal close')
        }

        // 延迟清理UI，让用户看到最终状态
        setTimeout(() => {
          if (taskStatus !== 'complete') {
            setTaskProgress('')
          }
        }, 3000)
      }
    }

    // 防护型的错误处理
    ws.onerror = (error) => {
      console.error('[WebSocket] Connection error:', error)

      // 只有在非重试连接时才显示连接错误
      if (!isRetryConnection) {
        setIsGenerating(false)
        setError('连接失败，请检查网络后重试')
      }
    }
  }, [updateRetryState, setTaskDataSafely, isUserRelatedError, isSystemTemporaryError, retryState.frontendInProgress, retryWithFrontendLogic])






  // 异步TTS处理函数 - 修改为WebSocket实现
  const handleAsyncGenerate = () => {
    // 1. 前置检查 - 使用 useMemo 计算的禁用状态
    if (isGenerateDisabled) return

    setIsGenerating(true)
    setError(null)
    setTaskStatus('processing')
    setTaskProgress('正在建立安全连接...')

    // 重置播放器状态
    setHasAudio(false)
    setAudioUrls({
      streamUrl: null,
      downloadUrl: null,
      secureStreamUrl: null
    })
    autoplayNextRef.current = true // 希望完成后自动播放

    // 【优化】重置逻辑任务状态，为新请求做准备
    resetLogicalTaskState() // 先清理旧的

    // 【核心】在请求开始时，就创建逻辑ID
    const newLogicalTaskId = generateLogicalTaskId()
    setLogicalTaskId(newLogicalTaskId)
    logicalTaskIdRef.current = newLogicalTaskId

    console.log('[TASK-MAPPING] Generated new logical task ID:', newLogicalTaskId)

    // 注意：此时还不要保存到sessionStorage或添加到任务中心，
    // 因为我们还没有物理ID。等到第一个'initialized'消息回来再做。

    // 【新增】重置所有重试状态（使用新的状态管理）
    updateRetryState(prev => ({
      ...prev,
      frontendAttempts: 0,
      datacenterAttempts: 0,
      backupApiAttempts: 0,
      frontendInProgress: false,
      usingBackupApi: false,
      currentBackupIndex: -1,
      isLocked: false,
      activeRetryType: null,
      frontendStartTime: 0,
      backupApiStartTime: 0,
      errorHistory: []
    }))

    // 重置其他状态
    setRetryCount(0)
    setExcludedLocations([])

    // 保存原始任务数据供重试使用
    const taskData = {
      input: text.trim(),
      voice: selectedVoice,
      model: selectedModel,
      stability: stability[0],
      similarity_boost: similarity[0],
      style: style[0],
      speed: speechRate[0],
      taskType: mode === 'dialogue' ? 'dialogue' : 'single',
      dialogueLines: mode === 'dialogue' ? dialogueLines : undefined
    }
    setTaskDataSafely(taskData) // 使用安全的数据设置函数

    const token = TokenManager.getAccessToken()
    if (!token) {
        setShowAuthDialog(true)
        setIsGenerating(false)
        return
    }

    // 2. 构建 WebSocket URL - 使用辅助函数确保一致性
    const wsUrl = buildWsUrl(mode);

    const ws = new WebSocket(wsUrl)

    // 3. 定义 WebSocket 生命周期事件处理

    // 连接成功后，发送任务参数
    ws.onopen = () => {
        setTaskProgress('连接成功，正在启动任务...')

        // 构建基础参数对象
        let requestParams: any = {
            action: 'start',
            token: token,
            model: selectedModel,
        }

        // 根据模式添加特定参数
        if (mode === 'single') {
            requestParams.input = text.trim();
            requestParams.voice = selectedVoice;
        } else if (mode === 'dialogue') {
            requestParams.taskType = 'dialogue';
            requestParams.dialogue = dialogueLines.map(line => ({
                voice: line.voice,
                text: line.text.trim()
            }));
        }

        // 根据不同模型传递不同的参数
        if (selectedModel === "eleven_v3") {
            // Eleven v3 模型只传递稳定性参数
            requestParams.stability = stability[0];
        } else {
            // 其他模型传递完整参数
            requestParams.stability = stability[0];
            requestParams.similarity_boost = similarity[0];
            requestParams.speed = speechRate[0];

            // 当选择 Eleven Turbo v2 或 Eleven Turbo v2.5 模型时，不传递 style 参数
            if (selectedModel !== "eleven_turbo_v2" && selectedModel !== "eleven_turbo_v2_5") {
                requestParams.style = style[0];
            }
        }

        ws.send(JSON.stringify(requestParams))
    }

    // 【修改】使用新的、带上下文的WebSocket处理器
    setupWebSocketHandlersWithRetryContext(ws)
  }

  // 保持原有的同步方法作为备用（向后兼容）
  const handleGenerate = handleAsyncGenerate

  const togglePlayback = async () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      try {
        // 优先使用安全的blob URL，如果没有则尝试加载
        if (audioUrls.secureStreamUrl) {
          if (audioRef.current.src !== audioUrls.secureStreamUrl) {
            audioRef.current.src = audioUrls.secureStreamUrl
          }
        } else if (audioUrls.streamUrl) {
          // 如果还没有安全URL，先加载
          console.log('[PLAYBACK] Loading secure audio for playback...')
          const secureUrl = await loadSecureAudio(audioUrls.streamUrl)
          if (secureUrl) {
            setAudioUrls(prev => ({
              ...prev,
              secureStreamUrl: secureUrl
            }))
            audioRef.current.src = secureUrl
          } else {
            throw new Error('Failed to load secure audio')
          }
        } else {
          throw new Error('No audio URL available')
        }

        // 等待播放Promise完成，确保音频真正开始播放
        await audioRef.current.play()
        setIsPlaying(true)
      } catch (error) {
        // 如果播放失败，保持UI状态一致
        console.error("Manual playback failed:", error)
        setIsPlaying(false)
        setError('播放失败，请检查网络连接或重新生成')
      }
    }
  }

  // 音频播放结束事件处理
  const handleAudioEnded = () => {
    setIsPlaying(false)
    setProgress(0)
    setCurrentTime("00:00")
    // 重置播放位置到开头
    if (audioRef.current) {
      audioRef.current.currentTime = 0
    }
  }

  // 下载音频文件 - 使用安全的认证下载
  const handleDownload = async () => {
    if (!audioUrls.downloadUrl) return

    try {
      // 生成年月日格式的文件名
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')

      const fileName = `tts_${year}${month}${day}_${hours}${minutes}${seconds}.mp3`

      // 使用安全的下载函数
      const success = await downloadSecureAudio(audioUrls.downloadUrl, fileName)

      if (success) {
        console.log('[DOWNLOAD] Download completed successfully with secure authentication')
      }
    } catch (error) {
      console.error('[DOWNLOAD] Download failed:', error)
      setError('下载失败，请重试')
    }
  }

  // 进度条交互功能 - 分离视觉更新和音频寻址以优化性能

  // 1. 仅更新视觉状态（进度条、时间文本），返回计算出的进度百分比
  const updateVisualProgress = (clientX: number, progressBarElement: HTMLElement): number => {
    if (!audioRef.current || !hasAudio) return 0

    const rect = progressBarElement.getBoundingClientRect()
    const clickX = clientX - rect.left
    const progressBarWidth = rect.width
    const clampedX = Math.max(0, Math.min(clickX, progressBarWidth))
    const clickProgress = progressBarWidth > 0 ? (clampedX / progressBarWidth) * 100 : 0

    setProgress(clickProgress)

    const duration = audioRef.current.duration
    if (duration > 0 && !isNaN(duration)) {
      const targetTime = (clickProgress / 100) * duration
      const clampedTime = Math.max(0, Math.min(targetTime, duration))
      const minutes = Math.floor(clampedTime / 60)
      const seconds = Math.floor(clampedTime % 60)
      setCurrentTime(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`)
    }
    return clickProgress
  }

  // 2. 更新音频的实际播放时间（一个昂贵的操作）
  const seekAudioTime = (progress: number) => {
    if (!audioRef.current || !hasAudio) return

    const duration = audioRef.current.duration
    if (duration > 0 && !isNaN(duration)) {
      const targetTime = (progress / 100) * duration
      const clampedTime = Math.max(0, Math.min(targetTime, duration))
      try {
        audioRef.current.currentTime = clampedTime
      } catch (error) {
        console.warn('更新音频时间失败:', error)
      }
    }
  }

  // 3. 进度条点击处理
  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const newProgress = updateVisualProgress(event.clientX, event.currentTarget)
    seekAudioTime(newProgress)
  }

  // 4. 进度条拖拽处理 - 优化以实现丝滑跟手效果
  const handleProgressMouseDown = (event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)

    const progressBarElement = event.currentTarget

    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault()
      // 使用 requestAnimationFrame 将视觉更新与浏览器渲染同步，实现最高流畅度
      requestAnimationFrame(() => {
        updateVisualProgress(e.clientX, progressBarElement)
      })
    }

    const handleMouseUp = (e: MouseEvent) => {
      e.preventDefault()

      // 在拖拽结束后，才执行昂贵的音频寻址操作
      const finalProgress = updateVisualProgress(e.clientX, progressBarElement)
      seekAudioTime(finalProgress)

      setIsDragging(false)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('mouseleave', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove, { passive: false })
    document.addEventListener('mouseup', handleMouseUp, { passive: false })
    document.addEventListener('mouseleave', handleMouseUp, { passive: false })
  }

  // 声音预览功能 - 增强版本，支持全局音频控制
  const handleVoicePreview = async (previewUrl: string | null, voiceId: string) => {
    if (!previewUrl) return; // 如果没有预览URL，则不执行任何操作

    try {
      // 停止当前正在播放的预览音频
      if (currentPreviewAudio) {
        currentPreviewAudio.pause()
        currentPreviewAudio.currentTime = 0
        const isDifferentVoice = previewingVoice !== voiceId;
        setCurrentPreviewAudio(null)
        setPreviewingVoice(null)
        // 如果点击的是不同的声音，则立即播放新声音
        if (!isDifferentVoice) return;
      }

      // 创建新的音频实例
      const audio = new Audio(previewUrl)

      // 设置当前播放的音频
      setCurrentPreviewAudio(audio)
      setPreviewingVoice(voiceId)

      // 添加播放结束事件监听
      audio.addEventListener('ended', () => {
        setCurrentPreviewAudio(null)
        setPreviewingVoice(null)
      })

      // 添加错误事件监听
      audio.addEventListener('error', () => {
        console.error('Preview audio failed to load:', previewUrl)
        setCurrentPreviewAudio(null)
        setPreviewingVoice(null)
      })

      // 播放音频
      await audio.play()
    } catch (error) {
      console.error('Preview audio failed:', error)
      setCurrentPreviewAudio(null)
      setPreviewingVoice(null)
    }
  }

  const LoadingAnimation = () => (
    <div className="flex flex-col items-center gap-2">
      <div className="flex items-center gap-3">
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-white rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.15}s` }}
            />
          ))}
        </div>
        <span className="animate-pulse">
          {retryState.frontendInProgress
            ? `正在重试... (${retryState.frontendAttempts}/${retryState.frontendMaxAttempts})`
            : taskStatus === 'processing' ? '生成中...' : '处理中...'
          }
        </span>
      </div>
      {taskProgress && (
        <div className="text-xs text-white/80 text-center max-w-xs">
          {taskProgress}
        </div>
      )}
    </div>
  )



  // 触发为对话行选择声音 - 保持向后兼容
  const handleEditDialogueVoice = (lineId: number) => {
    const lineToEdit = dialogueLines.find(line => line.id === lineId)
    if (lineToEdit) {
      // 同步主选择器的声音为当前行的声音
      setSelectedVoice(lineToEdit.voice)
    }
    setEditingDialogueLineId(lineId)
    setActiveDialogueLineId(lineId) // 激活当前行
    setIsDropdownOpen(true) // 打开下拉菜单
  }

  // 新增：激活对话行处理函数
  const handleSelectDialogueLine = (lineId: number) => {
    setActiveDialogueLineId(lineId)
    // 同时设置为编辑状态以保持兼容性
    setEditingDialogueLineId(lineId)
  }

  // 新增：处理文本输入框焦点事件
  const handleDialogueTextFocus = (lineId: number) => {
    setActiveDialogueLineId(lineId)
    // 当用户聚焦输入框时，我们认为他想打字而不是选声音，所以关闭下拉菜单
    setIsDropdownOpen(false)
    setEditingDialogueLineId(null) // 清除编辑声音状态
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 p-6 relative overflow-hidden">
      {/* 优化后的背景装饰组件 - 使用 React.memo 隔离重渲染 */}
      {isClient && (
        <>
          <FloatingParticles />
          <AnimatedBackgroundBlobs />
        </>
      )}

      <div
        className={`page-transition-optimized max-w-7xl mx-auto transition-all duration-1000 ${isPageLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
      >
        {/* Enhanced Header */}
        <div className="mb-6">
          {/* Navigation Bar - 改为顶部完整导航栏 */}
          <div className="relative z-40 flex items-center justify-between mb-4 p-4 bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-100">
            {/* 左侧标题区域 */}
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md opacity-50" />
                <div className="relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Mic className="w-6 h-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-gradient-optimized text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent animate-gradient">
                  AI 语音工作室
                </h1>
                <div className="h-0.5 w-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-1" />
              </div>
            </div>

            {/* 右侧操作区域 */}
            <div className="flex items-center gap-2 lg:gap-4">
              {/* 任务中心按钮 */}
              <TaskCenter
                ref={taskCenterRef}
                getActualTaskIds={getActualTaskIdForRefresh}
              />

              {/* 优化后的充值按钮组件 - 使用 React.memo 隔离重渲染 */}
              <RechargeButton />

              {/* 优化后的用户菜单组件 - 使用 React.memo 隔离重渲染 */}
              <UserMenuButton
                userEmail={userEmail}
                showUserMenu={showUserMenu}
                onToggleMenu={handleToggleUserMenu}
                onChangePassword={handleOpenChangePasswordDialog}
                onLogout={handleLogout}
                userMenuRef={userMenuRef}
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-6">
          {/* Left Column - Enhanced Main Workspace */}
          <div className="lg:col-span-2 space-y-3 lg:space-y-6 order-2 lg:order-1">
            {/* Enhanced Text Input Module */}
            <Card className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <CardContent className="pt-4 px-4 pb-2 sm:pt-6 sm:px-6 sm:pb-3 lg:pt-8 lg:px-7 lg:pb-4 relative">
                <div className="mb-2">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl">
                        <Zap className="w-6 h-6 text-blue-600" />
                      </div>
                      <h2 className="text-gradient-optimized text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        文本转语音
                      </h2>
                    </div>

                    {/* 限时开放提示文字 - 仅在临时开关开启时显示 */}
                    <div className="flex flex-col items-end">
                      {TEMP_DISABLE_PRO_RESTRICTION && (
                        <div className="mb-2 inline-flex items-center gap-2 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200/50 text-orange-700 text-sm font-medium px-3 py-1.5 rounded-lg shadow-sm animate-pulse">
                          <span className="w-2 h-2 bg-orange-500 rounded-full animate-ping"></span>
                          <span>"多人对话"功能限时开放中！</span>
                        </div>
                      )}

                      {/* 模式切换按钮 - 渐变滑动开关风格 */}
                      <div className="relative bg-gradient-to-r from-slate-100 via-gray-100 to-slate-100 p-1 rounded-2xl shadow-inner border border-gray-200/50 backdrop-blur-sm">
                      {/* 滑动背景指示器 */}
                      <div
                        className={`absolute top-1 bottom-1 w-[calc(50%-2px)] bg-gradient-to-r rounded-xl shadow-lg transition-all duration-500 ease-out transform ${
                          mode === 'single'
                            ? 'left-1 from-blue-500 via-blue-600 to-indigo-600'
                            : 'left-[calc(50%+2px)] from-purple-500 via-purple-600 to-pink-600'
                        }`}
                        style={modeToggleShadowStyle}
                      />

                      {/* 按钮容器 */}
                      <div className="relative flex items-center">
                        <button
                          onClick={() => handleModeChange('single')}
                          className={`relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ${
                            mode === 'single'
                              ? 'text-white drop-shadow-sm'
                              : 'text-gray-600 hover:text-gray-800'
                          }`}
                        >
                          <span className="relative z-10 flex items-center justify-center gap-2">
                            <User className="w-4 h-4" />
                            单人模式
                          </span>
                        </button>

                        <button
                          onClick={() => handleModeChange('dialogue')}
                          className={`relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ${
                            mode === 'dialogue'
                              ? 'text-white drop-shadow-sm'
                              : 'text-gray-600 hover:text-gray-800'
                          }`}
                        >
                          {/* 脉冲光环动画 - 仅在单人模式时显示，提示新功能 */}
                          {mode === 'single' && (
                            <>
                              {/* 外层光环 */}
                              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-purple-400/30 animate-pulse-ring-outer pointer-events-none" />
                              {/* 中层光环 */}
                              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 animate-pulse-ring-middle pointer-events-none" />
                              {/* 内层光环 */}
                              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-purple-600/10 animate-pulse-ring-inner pointer-events-none" />
                            </>
                          )}

                          <span className="relative z-10 flex items-center justify-center gap-2">
                            <Users className="w-4 h-4" />
                            多人对话
                          </span>
                          {/* PRO标识 - 绝对定位徽章 */}
                          <span className="absolute -top-1 -right-1 z-20 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg border border-white/20 transform scale-90 hover:scale-100 transition-transform duration-200">
                            PRO
                          </span>
                        </button>
                      </div>

                        {/* 光泽效果 */}
                        <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 rounded-2xl pointer-events-none" />
                      </div>
                    </div>
                  </div>

                  {/* v3模型提示和批量操作按钮 */}
                  {selectedModel === "eleven_v3" && (
                    <div className="mb-2 flex items-center justify-between">
                      <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 text-purple-700 text-sm font-medium px-3 py-2 rounded-lg shadow-sm">
                        <span>✨ v3模型支持情感标注词 目前处于测试阶段</span>
                        <button
                          onClick={() => {
                            // 定义两个示例文本和对应的声音
                            const examples = [
                              {
                                text: `Okay, you are NOT going to believe this. You know how I've been totally stuck? [frustrated sigh] I was ready to give up. Then last night, just doodling, this random phrase popped into my head. Typed it out and—bam—FLOODGATES. Everything clicked. Character, ending, all of it. I stayed up till 3 AM, typing like a maniac [laughs]. And it's GOOD. It finally feels alive. Like it has a soul. What was a chore now feels like... MAGIC. [happy gasp] I'm still buzzing!`,
                                voiceId: "iP95p4xoKVk53GoZ742B" // Chris
                              },
                              {
                                text: `In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the "burn it all down" kind - [exhales] he was gentle, wise, with eyes like old stars. [softly] Even the birds fell silent when he passed.`,
                                voiceId: "cgSgspJ2msm6clMCkdW9" // Jessica
                              }
                            ];

                            // 使用当前示例并切换到下一个
                            const currentExample = examples[currentExampleIndex];

                            // 设置文本和声音
                            setText(currentExample.text);
                            setSelectedVoice(currentExample.voiceId);

                            // 切换到下一个示例索引
                            setCurrentExampleIndex((currentExampleIndex + 1) % examples.length);
                          }}
                          className="px-2 py-1 bg-purple-100 hover:bg-purple-200 text-purple-800 text-xs font-semibold rounded transition-colors duration-200 hover:scale-105 transform"
                        >
                          查看示例
                        </button>
                        <button
                          onClick={() => setShowV3IntroDialog(true)}
                          className="px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 hover:from-blue-200 hover:to-indigo-200 text-blue-800 text-xs font-semibold rounded transition-all duration-200 hover:scale-105 transform flex items-center gap-1"
                        >
                          <span>💡</span>
                          <span>了解更多</span>
                        </button>
                      </div>

                      {/* 批量操作按钮 - 仅在多人对话模式显示 */}
                      {mode === 'dialogue' && (
                        <BatchOperations
                          dialogueLines={dialogueLines}
                          voices={voices}
                          onImport={handleBatchImport}
                          className="ml-auto"
                        />
                      )}
                    </div>
                  )}
                </div>

                {mode === 'single' ? (
                  <>
                    <div className="relative">
                      {emotionalTagCount}

                      <RichTextInput
                        value={text}
                        onChange={setText}
                        onFocus={() => setTextareaFocused(true)}
                        onBlur={() => setTextareaFocused(false)}
                        placeholder="请输入要转换的文本..."
                        className={`min-h-[160px] sm:min-h-[200px] lg:min-h-[270px] max-h-[270px] overflow-y-auto text-base lg:text-lg leading-relaxed resize-none border-2 transition-all duration-500 bg-gradient-to-br from-white to-gray-50/50 p-4 outline-none scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ${
                          textareaFocused
                            ? "border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.01]"
                            : "border-gray-200 hover:border-gray-300 hover:shadow-lg"
                        }`}
                        maxLength={maxChars}
                        onMaxLengthExceeded={handleMaxLengthExceeded}
                      />
                    </div>

                    {/* 按钮和字符计数器区域 */}
                    <div className="flex items-center justify-between mt-2">
                      {/* 自动生成标签按钮 - 仅在v3模型时显示 */}
                      {selectedModel === "eleven_v3" ? (
                        <button
                          onClick={handleAutoGenerateTags}
                          disabled={isGeneratingTags || !text.trim() || (isClient && (!auth.isLoggedIn() || !userStatus.isVip || Date.now() > userStatus.expireAt))}
                          className={`group relative flex items-center gap-2 px-3 py-1 text-sm font-medium rounded-lg transition-all duration-300 ${
                            isGeneratingTags || !text.trim() || (isClient && (!auth.isLoggedIn() || !userStatus.isVip || Date.now() > userStatus.expireAt))
                              ? "text-gray-400 bg-gray-100 border border-gray-200 cursor-not-allowed"
                              : "text-purple-600 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 hover:scale-105 hover:shadow-md"
                          }`}
                        >
                          {!isGeneratingTags && (
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                          )}
                          {isGeneratingTags ? (
                            <div className="w-4 h-4 relative z-10">
                              <div className="w-4 h-4 border-2 border-purple-300 border-t-purple-600 rounded-full animate-spin"></div>
                            </div>
                          ) : (
                            <Sparkles className="w-4 h-4 relative z-10 transition-all duration-300 group-hover:scale-110" />
                          )}
                          <span className="relative z-10">
                            {isGeneratingTags ? "生成中..." : "自动标注"}
                          </span>
                          {/* 权限提示 - 只在客户端渲染避免水合失败 */}
                          {isClient && (!auth.isLoggedIn() || !userStatus.isVip || Date.now() > userStatus.expireAt) && (
                            <div className="absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">!</span>
                            </div>
                          )}
                        </button>
                      ) : (
                        /* 非v3模型时显示提示信息 */
                        <div className="flex items-center gap-2 px-3 py-1 text-sm text-gray-500 bg-gray-50 border border-gray-200 rounded-lg">
                          <Sparkles className="w-4 h-4 text-gray-400" />
                          <span>自动标注功能仅在 Eleven v3 模型下可用</span>
                        </div>
                      )}

                      {/* 字符计数器 */}
                      <div
                        className={`text-sm px-3 py-1 rounded-lg transition-all duration-300 ${
                          text.length > maxChars * 0.8 ? "bg-orange-100 text-orange-700" : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        <span className="font-mono">{text.length}</span> / {maxChars}
                      </div>
                    </div>

                    {/* 权限提示信息 - 只在客户端渲染避免水合失败 */}
                    {isClient && (!auth.isLoggedIn() || !userStatus.isVip || Date.now() > userStatus.expireAt) && (
                      <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <div className="flex items-center gap-2 text-amber-700">
                          <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            {!auth.isLoggedIn()
                              ? "请先登录后使用自动标注功能"
                              : !userStatus.isVip
                                ? "自动标注功能需要会员权限"
                                : "会员已过期，请续费后使用"}
                          </span>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="space-y-3">
                    <SmartDialogueList
                      dialogueLines={dialogueLines}
                      voices={voices}
                      voiceIconMapping={voiceIconMapping}
                      voiceIcons={voiceIcons}
                      activeDialogueLineId={activeDialogueLineId}
                      onSelectLine={handleSelectDialogueLine}
                      onUpdateText={handleUpdateDialogueText}
                      onRemoveLine={removeDialogueLine}
                      onTextInputFocus={handleDialogueTextFocus}
                      onEditVoice={handleEditDialogueVoice}
                      containerHeight={350}
                      virtualThreshold={20}
                    />
                    {/* 添加对话按钮 */}
                    <button
                      onClick={addDialogueLine}
                      disabled={isAddingDialogueLine}
                      className={`w-full flex items-center justify-center gap-2 py-3 text-sm font-semibold border-2 border-dashed rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        isAddingDialogueLine
                          ? "text-gray-400 bg-gray-50 border-gray-200 cursor-not-allowed"
                          : "text-blue-600 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:scale-102 hover:shadow-md focus:ring-blue-400"
                      }`}
                    >
                      {isAddingDialogueLine ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                          添加中...
                        </>
                      ) : (
                        <>
                          <UserCheck className="w-4 h-4" />
                          添加对话
                        </>
                      )}
                    </button>
                  </div>
                )}

                {/* 错误提示 - 支持不同错误类型的样式 */}
                {(error || tagGenerationError) && (
                  <div className="mt-4 space-y-2">
                    {error && (
                      <div className={`p-4 rounded-xl border ${
                        error.includes('违反') || error.includes('屏蔽') || error.includes('服务条款')
                          ? 'bg-orange-50 border-orange-200'
                          : error.includes('配额') || error.includes('会员') || error.includes('权限')
                          ? 'bg-yellow-50 border-yellow-200'
                          : 'bg-red-50 border-red-200'
                      }`}>
                        <div className={`flex items-center gap-2 ${
                          error.includes('违反') || error.includes('屏蔽') || error.includes('服务条款')
                            ? 'text-orange-700'
                            : error.includes('配额') || error.includes('会员') || error.includes('权限')
                            ? 'text-yellow-700'
                            : 'text-red-700'
                        }`}>
                          <div className={`w-2 h-2 rounded-full ${
                            error.includes('违反') || error.includes('屏蔽') || error.includes('服务条款')
                              ? 'bg-orange-500'
                              : error.includes('配额') || error.includes('会员') || error.includes('权限')
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                          }`}></div>
                          <span className="text-sm font-medium">{error}</span>
                        </div>
                      </div>
                    )}
                    {tagGenerationError && (
                      <div className="p-4 bg-orange-50 border border-orange-200 rounded-xl">
                        <div className="flex items-center gap-2 text-orange-700">
                          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                          <span className="text-sm font-medium">{tagGenerationError}</span>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Audio Player - Reference Design Style */}
            {/* 当选择v3模型且无音频时隐藏整个卡片 */}
            {!(selectedModel === "eleven_v3" && !hasAudio) && (
              <Card className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 rounded-2xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <CardContent className="p-0 relative">
                  {!hasAudio ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center px-6">
                      <div className="w-14 h-14 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-3">
                        <Volume2 className="w-7 h-7 text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">您的音频杰作将在此呈现</h3>
                      <p className="text-gray-500 text-sm">
                        选择声音并点击生成，创造完美的语音体验
                      </p>
                    </div>
                  ) : (
                  <div>
                    {/* 隐藏的audio元素 */}
                    <audio
                      ref={audioRef}
                      src={audioUrls.secureStreamUrl || undefined}
                      onEnded={handleAudioEnded}
                      style={staticStyles.audioHidden}
                    />

                    {/* 音频播放器 - 参考图样式：垂直布局 */}
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 px-6 py-5 pb-8 relative">
                      <div className="space-y-4">
                        {/* 上方：控制按钮区域 */}
                        <div className="relative flex items-center justify-center gap-6">
                          {/* 快退按钮 */}
                          <Button
                            onClick={() => {
                              if (audioRef.current) {
                                audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 10)
                              }
                            }}
                            variant="ghost"
                            size="sm"
                            className="w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                            </svg>
                          </Button>

                          {/* 播放/暂停按钮 */}
                          <Button
                            onClick={togglePlayback}
                            size="sm"
                            className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex-shrink-0 border-0 hover:scale-105"
                          >
                            {isPlaying ? (
                              <Pause className="w-5 h-5" />
                            ) : (
                              <Play className="w-5 h-5 ml-0.5" />
                            )}
                          </Button>

                          {/* 快进按钮 */}
                          <Button
                            onClick={() => {
                              if (audioRef.current) {
                                audioRef.current.currentTime = Math.min(audioRef.current.duration || 0, audioRef.current.currentTime + 10)
                              }
                            }}
                            variant="ghost"
                            size="sm"
                            className="w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M16 18h2V6h-2zm-3.5-6L4 6v12z"/>
                            </svg>
                          </Button>

                          {/* 下载按钮 - 右侧绝对定位 */}
                          <Button
                            onClick={handleDownload}
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>

                        {/* 下方：进度条和时间 */}
                        <div className="relative pb-6">
                          {/* 进度条 */}
                          <div
                            className={`relative bg-gray-200 rounded-full cursor-pointer transition-all duration-200 ${
                              isDragging ? 'h-1.5' : 'h-1 hover:h-1.5'
                            }`}
                            onClick={handleProgressClick}
                            onMouseDown={handleProgressMouseDown}
                            title={`拖拽或点击调整播放进度 (${currentTime} / ${audioDuration})`}
                            style={progressBarStyle}
                          >
                            {/* 进度条填充 */}
                            <div
                              className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-200"
                              style={progressFillStyle}
                            />

                            {/* 拖拽手柄 */}
                            {progress > 0 && (
                              <div
                                className={`absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg ${
                                  isDragging
                                    ? 'scale-125 opacity-100'
                                    : 'opacity-0 hover:opacity-100 transition-all duration-150'
                                }`}
                                style={progressThumbStyle}
                              />
                            )}
                          </div>

                          {/* 时间显示 - 绝对定位在进度条两端，增加底部空间 */}
                          <div className="absolute top-6 left-0 text-xs font-mono text-gray-600 font-medium">
                            {currentTime}
                          </div>
                          <div className="absolute top-6 right-0 text-xs font-mono text-gray-600 font-medium">
                            {audioDuration}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            )}
          </div>

          {/* Right Column - Enhanced Control Panel */}
          <div className="space-y-2 lg:space-y-4 relative z-0 order-1 lg:order-2">
            {/* 统一的声音选择器 - 适用于单人模式和多人对话模式 */}
            <Card
              className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative"
              style={{ zIndex: 100 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <CardContent className="p-6 relative">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl">
                    {mode === 'dialogue' && editingDialogueLineId !== null ? (
                      <Users className="w-5 h-5 text-purple-600" />
                    ) : (
                      <Mic className="w-5 h-5 text-green-600" />
                    )}
                  </div>
                  <h3 className="text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    {mode === 'dialogue' && editingDialogueLineId !== null ? '为对话行选择声音' : '选择声音'}
                  </h3>
                </div>

                <div className="relative" ref={dropdownRef}>
                  {/* Dropdown Trigger - keep existing button code */}
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className={`w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ${
                      isDropdownOpen
                        ? "border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.02]"
                        : "border-gray-200 hover:border-gray-300 hover:shadow-lg"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {selectedVoice ? (
                        <>
                          <div className="relative w-6 h-6 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/trigger:scale-110">
                            <img
                              src={voiceIconMapping[selectedVoice] || voiceIcons[0]}
                              alt={voices.find((v) => v.id === selectedVoice)?.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // 如果图片加载失败，显示备用的文字头像
                                const target = e.target as HTMLImageElement
                                target.style.display = 'none'
                                const parent = target.parentElement
                                if (parent) {
                                  parent.innerHTML = `
                                    <div class="w-full h-full rounded-full flex items-center justify-center text-white text-sm font-bold ${
                                      voices.find((v) => v.id === selectedVoice)?.gender === "male"
                                        ? "bg-gradient-to-r from-blue-400 to-blue-600"
                                        : voices.find((v) => v.id === selectedVoice)?.gender === "female"
                                          ? "bg-gradient-to-r from-pink-400 to-purple-600"
                                          : "bg-gradient-to-r from-purple-400 to-indigo-600"
                                    }">
                                      ${voices.find((v) => v.id === selectedVoice)?.name[0] || '?'}
                                    </div>
                                  `
                                }
                              }}
                            />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900 text-base">
                              {voices.find((v) => v.id === selectedVoice)?.name}
                            </div>
                            {/* 新增逻辑: 无论是否在编辑，只要在对话模式且有激活行，就显示提示 */}
                            {mode === 'dialogue' && activeDialogueLineId !== null && (
                              <div className="text-xs text-gray-500">
                                {editingDialogueLineId !== null
                                  ? `正在编辑: 对话 ${dialogueLines.findIndex(line => line.id === editingDialogueLineId) + 1}`
                                  : `当前: 对话 ${dialogueLines.findIndex(line => line.id === activeDialogueLineId) + 1}`
                                }
                              </div>
                            )}
                          </div>
                        </>
                      ) : (
                        <div className="text-gray-500 text-base">请选择声音...</div>
                      )}
                    </div>
                    <ChevronDown
                      className={`w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-blue-500 ${
                        isDropdownOpen ? "rotate-180 text-blue-500" : ""
                      }`}
                    />
                  </button>

                  {/* Enhanced Dropdown Panel with Search and Filter */}
                  {isDropdownOpen && (
                    <div
                      className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100"
                      style={{ zIndex: 9999 }}
                    >
                      {/* Enhanced Search Box with Filter Button */}
                      <div className="p-4 border-b border-gray-100/60 bg-gradient-to-r from-gray-50/30 to-blue-50/20">
                        <div className="flex items-center gap-3">
                          {/* Search Input */}
                          <div className="relative group flex-1">
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10" />
                            <input
                              ref={searchInputRef}
                              type="text"
                              placeholder="搜索声音名称或描述..."
                              value={searchQuery}
                              onChange={(e) => setSearchQuery(e.target.value)}
                              className="relative w-full pl-11 pr-11 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"
                            />
                            {searchQuery && (
                              <button
                                onClick={() => setSearchQuery("")}
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-red-500 hover:scale-110 transition-all duration-200 z-10"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                          </div>

                          {/* Filter Button */}
                          <div className="relative">
                            <button
                              onClick={openFilterModal}
                              className={`relative p-3 border-2 rounded-2xl transition-all duration-300 flex items-center justify-center group/filter shadow-sm hover:shadow-lg ${
                                selectedGender !== "all" || selectedLanguage !== "all" || searchQuery.trim() || showOnlyFavorites
                                  ? "border-purple-400 bg-gradient-to-r from-purple-50 to-pink-50 text-purple-600 shadow-purple-100/50"
                                  : "border-gray-200 bg-white hover:border-gray-300 text-gray-500 hover:text-purple-500"
                              }`}
                              title="筛选选项"
                            >
                              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl opacity-0 group-hover/filter:opacity-100 transition-all duration-300"></div>
                              <Filter className="w-4 h-4 relative z-10 transition-all duration-300 group-hover/filter:scale-110" />
                              {/* Active Filter Indicator */}
                              {(selectedGender !== "all" || selectedLanguage !== "all" || searchQuery.trim()) && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-2 border-white shadow-sm">
                                  <div className="absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75" />
                                </div>
                              )}
                            </button>
                          </div>

                          {/* 动态按钮：无筛选时显示收藏按钮，有筛选时显示清除按钮 */}
                          {(selectedGender !== "all" || selectedLanguage !== "all" || searchQuery.trim() || showOnlyFavorites) ? (
                            // 有筛选条件时显示清除按钮
                            <div className="relative">
                              <button
                                onClick={() => {
                                  setSelectedGender("all")
                                  setSelectedLanguage("all")
                                  setSearchQuery("")
                                  setShowOnlyFavorites(false)
                                }}
                                className="relative p-3 border-2 border-red-200 bg-gradient-to-r from-red-50 to-orange-50 text-red-600 rounded-2xl transition-all duration-300 flex items-center justify-center group/clear shadow-sm hover:shadow-lg hover:border-red-300 hover:from-red-100 hover:to-orange-100"
                                title="清除所有筛选条件"
                              >
                                <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-orange-500/5 rounded-2xl opacity-0 group-hover/clear:opacity-100 transition-all duration-300"></div>
                                <X className="w-4 h-4 relative z-10 transition-all duration-300 group-hover/clear:scale-110" />
                              </button>
                            </div>
                          ) : (
                            // 无筛选条件时显示收藏按钮
                            <div className="relative">
                              <button
                                onClick={() => setShowOnlyFavorites(true)}
                                className="relative p-3 border-2 border-gray-200 bg-white hover:border-pink-300 text-gray-500 hover:text-pink-500 rounded-2xl transition-all duration-300 flex items-center justify-center group/favorite shadow-sm hover:shadow-lg hover:bg-gradient-to-r hover:from-pink-50 hover:to-red-50"
                                title="仅显示收藏声音"
                              >
                                <div className="absolute inset-0 bg-gradient-to-r from-pink-500/5 to-red-500/5 rounded-2xl opacity-0 group-hover/favorite:opacity-100 transition-all duration-300"></div>
                                <Heart className="w-4 h-4 relative z-10 transition-all duration-300 group-hover/favorite:scale-110" />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Filter Status Indicator - 当有筛选条件时显示 */}
                      {(selectedGender !== "all" || selectedLanguage !== "all" || searchQuery.trim() || showOnlyFavorites) && (
                        <div className="px-4 py-2 bg-gradient-to-r from-blue-50/80 to-purple-50/60 border-t border-gray-100/60 animate-fade-in">
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center gap-2 text-gray-600">
                              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                              <span>当前筛选：</span>
                              <div className="flex items-center gap-1">
                                {selectedGender !== "all" && (
                                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center gap-1">
                                    {selectedGender === "male" ? <MaleIcon className="w-3 h-3" /> :
                                     selectedGender === "female" ? <FemaleIcon className="w-3 h-3" /> :
                                     <NeutralIcon className="w-3 h-3" />}
                                    {selectedGender === "male" ? "男生" : selectedGender === "female" ? "女生" : "中性"}
                                  </span>
                                )}
                                {selectedLanguage !== "all" && (
                                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium flex items-center gap-1">
                                    {selectedLanguage === "en" ? <USFlagIcon className="w-3 h-3" /> :
                                     selectedLanguage === "ja" ? <JPFlagIcon className="w-3 h-3" /> :
                                     selectedLanguage === "es" ? <ESFlagIcon className="w-3 h-3" /> :
                                     selectedLanguage === "ko" ? <KRFlagIcon className="w-3 h-3" /> :
                                     <FRFlagIcon className="w-3 h-3" />}
                                    {selectedLanguage === "en" ? "英语" : selectedLanguage === "ja" ? "日语" : selectedLanguage === "es" ? "西班牙语" : selectedLanguage === "ko" ? "韩语" : "法语"}
                                  </span>
                                )}
                                {searchQuery.trim() && (
                                  <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium flex items-center gap-1">
                                    <Search className="w-3 h-3" />
                                    "{searchQuery.length > 10 ? searchQuery.substring(0, 10) + "..." : searchQuery}"
                                  </span>
                                )}
                                {/* 【新增】收藏筛选状态显示 */}
                                {showOnlyFavorites && (
                                  <span className="px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs font-medium flex items-center gap-1">
                                    <Heart className="w-3 h-3 fill-current" />
                                    仅收藏
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="text-blue-600 font-medium">
                              {filteredVoices.length} 个结果
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Voice List - 使用新的 VoiceSelectionList 组件 */}
                      <div ref={dropdownScrollRef}>
                        <VoiceSelectionList
                          filteredVoices={filteredVoices}
                          onSelectVoice={handleSelectVoice}
                          currentVoiceId={selectedVoice}
                          previewingVoice={previewingVoice}
                          handleVoicePreview={handleVoicePreview}
                          voiceIconMapping={voiceIconMapping}
                          voiceIcons={voiceIcons}
                          listHeightClass="max-h-80"
                          // 【新增】收藏功能参数
                          favoriteVoiceIds={favoriteVoiceIds}
                          onToggleFavorite={toggleVoiceFavorite}
                          showFavoriteButton={true}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Model Selector */}
            <ModelSelector
              selectedModel={selectedModel}
              isModelDropdownOpen={isModelDropdownOpen}
              availableModels={availableModels}
              onModelChange={handleModelChange}
              onToggleDropdown={handleToggleModelDropdown}
              modelDropdownRef={modelDropdownRef}
            />

            {/* Enhanced Parameter Tuning */}
            <ParameterSliders
              stability={stability}
              similarity={similarity}
              style={style}
              speechRate={speechRate}
              selectedModel={selectedModel}
              onStabilityChange={handleStabilityChange}
              onSimilarityChange={handleSimilarityChange}
              onStyleChange={handleStyleChange}
              onSpeechRateChange={handleSpeechRateChange}
            />

            {/* Enhanced Generate Button with Aurora Effect */}
            <Button
              onClick={handleGenerate}
              disabled={isGenerateDisabled}
              className={`button-hover-optimized w-full text-lg lg:text-xl font-bold relative overflow-hidden group rounded-3xl transition-all duration-500 transform hover:scale-105 disabled:scale-100 shadow-2xl hover:shadow-3xl ${
                isGenerating ? 'h-16 sm:h-18 lg:h-20' : 'h-12 sm:h-14 lg:h-16'
              } ${isGenerateDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              style={staticStyles.generateButtonTransparent}
            >
              {/* Aurora Effect Background - CSS Animation Implementation */}
              <div className="absolute inset-0 rounded-3xl overflow-hidden">
                <GenerateButtonAurora className="rounded-3xl" />
              </div>

              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 rounded-3xl" />

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl" />

              {/* Button content */}
              <div className="relative z-10 text-white font-bold">
                {isGenerating || retryState.frontendInProgress ? (
                  <LoadingAnimation />
                ) : (
                  <div className="flex items-center gap-3 text-xl">
                    生成音频
                  </div>
                )}
              </div>
            </Button>
          </div>
        </div>
      </div>

      {/* 会员弹窗 */}
      <Dialog open={showVipDialog} onOpenChange={setShowVipDialog}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
            <div className="flex items-center gap-6">
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                配额不足
              </DialogTitle>
              <div className="relative px-3 py-1.5 rounded-full shadow-sm hover:scale-105 transition-all duration-300 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-pink-400 via-red-400 via-orange-400 via-yellow-400 via-green-400 via-teal-400 via-blue-400 via-indigo-400 via-purple-400 to-pink-400 animate-rainbow-bg rounded-full"></div>
                <div className="relative bg-white/90 backdrop-blur-sm rounded-full px-3 py-1.5 m-0.5">
                  <span className="text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    免费体验找客服微信sunshine-12-06
                  </span>
                </div>
              </div>
            </div>
            <DialogDescription className="text-gray-600 space-y-3">
              <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                <AlertTriangle className="w-5 h-5 text-orange-500 flex-shrink-0" />
                <span className="text-sm">您当前没有配音权限或配额已用完</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <Zap className="w-5 h-5 text-blue-500 flex-shrink-0" />
                <span className="text-sm">请充值获取会员权限后继续使用</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                <Clock className="w-5 h-5 text-green-500 flex-shrink-0" />
                <span className="text-sm">我们提供多种套餐选择，满足不同需求</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex-col sm:flex-row gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowVipDialog(false)}
              className="w-full sm:w-auto border-gray-300 hover:bg-gray-50"
            >
              稍后再说
            </Button>
            <Button
              onClick={() => {
                setShowVipDialog(false)
                window.location.href = "/recharge"
              }}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <CreditCard className="w-4 h-4 mr-2" />
              立即充值
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* PRO升级弹窗 */}
      <Dialog open={showProUpgradeDialog} onOpenChange={setShowProUpgradeDialog}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <DialogTitle className="text-2xl font-bold">
              解锁 <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">PRO</span> 功能
            </DialogTitle>
            <DialogDescription className="text-gray-600 space-y-3">
              <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <svg className="w-5 h-5 text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span className="text-sm">多人对话功能仅限 PRO 会员使用</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <Zap className="w-5 h-5 text-blue-500 flex-shrink-0" />
                <span className="text-sm">升级即可享受更多高级功能</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                <Clock className="w-5 h-5 text-green-500 flex-shrink-0" />
                <span className="text-sm">优先处理队列，更快生成速度</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex-col sm:flex-row gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowProUpgradeDialog(false)}
              className="w-full sm:w-auto border-gray-300 hover:bg-gray-50"
            >
              稍后再说
            </Button>
            <Button
              onClick={() => {
                setShowProUpgradeDialog(false)
                window.location.href = '/recharge'
              }}
              className="w-full sm:w-auto bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <CreditCard className="w-4 h-4 mr-2" />
              立即升级
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 登录过期弹窗 */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
            <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              登录已过期
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              <div className="flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <span className="text-sm">您的会话已过期，请重新登录以继续。</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button
              onClick={async () => {
                setShowAuthDialog(false)
                await auth.logout()
                window.location.href = "/login"
              }}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <LogOut className="w-4 h-4 mr-2" />
              重新登录
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        @keyframes gradient {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes rainbow-bg {
          0% {
            background-position: 0% 50%;
            filter: hue-rotate(0deg) saturate(1.2) brightness(1.1);
          }
          16.67% {
            background-position: 66.67% 50%;
            filter: hue-rotate(60deg) saturate(1.3) brightness(1.2);
          }
          33.33% {
            background-position: 133.33% 50%;
            filter: hue-rotate(120deg) saturate(1.4) brightness(1.1);
          }
          50% {
            background-position: 200% 50%;
            filter: hue-rotate(180deg) saturate(1.3) brightness(1.2);
          }
          66.67% {
            background-position: 266.67% 50%;
            filter: hue-rotate(240deg) saturate(1.2) brightness(1.1);
          }
          83.33% {
            background-position: 333.33% 50%;
            filter: hue-rotate(300deg) saturate(1.3) brightness(1.2);
          }
          100% {
            background-position: 400% 50%;
            filter: hue-rotate(360deg) saturate(1.2) brightness(1.1);
          }
        }
        @keyframes aurora-flow {
          0%, 100% {
            background-position: 0% 50%, 100% 20%, 50% 80%, 0% 50%;
            opacity: 0.8;
          }
          25% {
            background-position: 25% 30%, 75% 40%, 25% 60%, 25% 30%;
            opacity: 1;
          }
          50% {
            background-position: 50% 70%, 50% 60%, 75% 40%, 50% 70%;
            opacity: 0.9;
          }
          75% {
            background-position: 75% 40%, 25% 80%, 50% 20%, 75% 40%;
            opacity: 1;
          }
        }
        @keyframes aurora-wave {
          0% {
            background-position: -200% 0%;
            opacity: 0.6;
          }
          50% {
            background-position: 0% 0%;
            opacity: 0.8;
          }
          100% {
            background-position: 200% 0%;
            opacity: 0.6;
          }
        }
        @keyframes aurora-shimmer {
          0%, 100% {
            background-position: -300% -300%;
            opacity: 0.3;
          }
          50% {
            background-position: 300% 300%;
            opacity: 0.7;
          }
        }
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.05);
          }
        }
        @keyframes breathe-glow {
          0%, 100% {
            opacity: 0;
            transform: scale(0.8);
          }
          50% {
            opacity: 0.4;
            transform: scale(1.2);
          }
        }

        /* 拖拽时禁用文本选择 */
        ${isDragging ? `
          * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
          }
          body {
            cursor: grabbing !important;
          }
        ` : ''}
        .animate-float {
          animation: float 8s ease-in-out infinite;
          will-change: transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient 3s ease infinite;
          will-change: background-position;
          contain: layout style paint;
        }

        .animate-fade-in {
          animation: fade-in 0.5s ease-out forwards;
          will-change: transform, opacity;
          contain: layout style paint;
        }
        .animate-shimmer {
          animation: shimmer 2s ease-in-out infinite;
          will-change: transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-breathe {
          animation: breathe 3s ease-in-out infinite;
          will-change: opacity, transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-breathe-glow {
          animation: breathe-glow 4s ease-in-out infinite;
          will-change: opacity, box-shadow;
          contain: layout style paint;
        }
        .scrollbar-thin {
          scrollbar-width: thin;
        }

        .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
          background-color: #d1d5db;
          border-radius: 0.375rem;
        }

        .scrollbar-track-gray-100::-webkit-scrollbar-track {
          background-color: #f3f4f6;
        }

        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }

        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        .animate-spin-slow {
          animation: spin-slow 8s linear infinite;
          will-change: transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-rainbow-bg {
          background-size: 600% 100%;
          animation: rainbow-bg 6s cubic-bezier(0.4, 0, 0.2, 1) infinite;
          will-change: background-position, filter;
          contain: layout style paint;
        }

        /* 优化Tailwind内置动画类 */
        .animate-pulse {
          will-change: opacity;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-spin {
          will-change: transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-bounce {
          will-change: transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        .animate-ping {
          will-change: transform, opacity;
          contain: layout style paint;
          transform: translateZ(0);
        }
      `}</style>

      {/* 修改密码对话框 */}
      <Dialog open={showChangePasswordDialog} onOpenChange={setShowChangePasswordDialog}>
        <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              <Key className="w-5 h-5 text-blue-600" />
              修改密码
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              为了您的账户安全，请输入当前密码和新密码
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* 当前密码 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">当前密码</label>
              <div className="relative">
                <Input
                  type={showCurrentPassword ? "text" : "password"}
                  value={changePasswordData.currentPassword}
                  onChange={(e) => setChangePasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  placeholder="请输入当前密码"
                  className="pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* 新密码 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">新密码</label>
              <div className="relative">
                <Input
                  type={showNewPassword ? "text" : "password"}
                  value={changePasswordData.newPassword}
                  onChange={(e) => setChangePasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  placeholder="请输入新密码（至少6位）"
                  className="pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* 确认新密码 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">确认新密码</label>
              <div className="relative">
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  value={changePasswordData.confirmPassword}
                  onChange={(e) => setChangePasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="请再次输入新密码"
                  className="pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* 错误提示 */}
            {changePasswordError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">{changePasswordError}</span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowChangePasswordDialog(false)
                setChangePasswordData({
                  currentPassword: "",
                  newPassword: "",
                  confirmPassword: ""
                })
                setChangePasswordError("")
              }}
              disabled={isChangingPassword}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleChangePassword}
              disabled={isChangingPassword}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
            >
              {isChangingPassword ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  修改中...
                </div>
              ) : (
                "确认修改"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 密码修改成功提示对话框 */}
      <Dialog open={showPasswordSuccessDialog} onOpenChange={setShowPasswordSuccessDialog}>
        <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-md opacity-50" />
                <div className="relative p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full shadow-lg">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
              </div>
              密码修改成功
            </DialogTitle>
            <DialogDescription className="text-gray-600 text-center py-4 text-lg">
              您的密码已成功更新，请妥善保管新密码
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex justify-center">
            <Button
              onClick={() => setShowPasswordSuccessDialog(false)}
              className="px-8 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重要通知弹窗（ElevenLabs 内容审核机制） */}
      <Dialog open={showNoticeDialog} onOpenChange={setShowNoticeDialog}>
        <DialogContent className="sm:max-w-4xl max-h-[95vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-y-auto">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-50/80 via-red-50/60 to-yellow-50/80" />
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-3xl" />
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-200/30 to-orange-200/30 rounded-full blur-2xl" />

          {/* 【修改】自定义关闭按钮组 - 添加"不再提示"按钮 */}
          <div className="absolute top-4 right-4 z-20 flex items-center gap-2">
            {/* 新添加的"不再提示"按钮 */}
            <button
              onClick={handleHideNoticePermanently}
              className="group px-3 py-2 rounded-full text-sm font-medium bg-white/80 hover:bg-gray-100 text-gray-500 hover:text-red-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              不再提示
            </button>

            {/* 原有的 'X' 关闭按钮 */}
            <button
              onClick={() => setShowNoticeDialog(false)}
              className="group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110"
            >
              <div className="relative">
                {/* 背景光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300" />
                {/* 关闭图标 */}
                <svg
                  className="relative w-5 h-5 text-gray-600 group-hover:text-red-500 transition-colors duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
            </button>
          </div>

          {/* 内容区域 */}
          <div className="relative z-10">
            <DialogHeader className="text-center space-y-4 pb-2">
              {/* 标题区域 */}
              <div className="flex items-center justify-center gap-3 mb-2">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-red-400 rounded-full blur-lg opacity-50 animate-pulse" />
                  <div className="relative p-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 rounded-full shadow-xl">
                    <AlertTriangle className="w-8 h-8 text-white animate-bounce" style={staticStyles.alertTriangleAnimation} />
                  </div>
                </div>
              </div>

              <DialogTitle className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-yellow-600 bg-clip-text text-transparent leading-tight text-center">
                📢 重要公告
              </DialogTitle>

              <div className="text-2xl font-semibold text-center text-gray-800">
                关于 ElevenLabs 内容审核机制
              </div>

              {/* 分隔线 */}
              <div className="flex items-center justify-center gap-2 py-2">
                <div className="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1" />
                <div className="w-2 h-2 bg-gradient-to-r from-orange-400 to-red-400 rounded-full animate-pulse" />
                <div className="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1" />
              </div>
            </DialogHeader>

            <div className="space-y-3 py-1">
              {/* 问候语 */}
              <div className="text-center">
                <p className="text-xl text-gray-700 font-bold leading-relaxed font-medium">
                  尊敬的用户，您好！
                </p>
              </div>

              {/* 核心提醒卡片 */}
              <div className="bg-gradient-to-r from-red-50/90 to-orange-50/90 rounded-3xl p-8 border-2 border-red-200/60 shadow-xl">
                <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center justify-center gap-3">
                  <AlertTriangle className="w-7 h-7 text-red-600" />
                  内容政策提醒
                </h3>

                <div className="text-center mb-6">
                  <p className="text-lg text-gray-700 leading-relaxed">
                    请注意 ElevenLabs 官方会对所有提交的文本内容进行<span className="font-semibold text-red-600">自动审核</span>。
                  </p>
                </div>

                {/* 可能被拦截的内容类型 */}
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-red-200/60 shadow-lg">
                  <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2">
                    ⚠️ 可能被拦截的内容
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">暴力或威胁性内容</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">仇恨言论或歧视性内容</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">成人或性暗示内容</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">政治敏感内容</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">版权保护的内容（如歌词、台词等）</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">可能用于欺诈的内容</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                        <span className="text-red-600 font-bold">🚫</span>
                        <span className="text-gray-700">其他违反 ElevenLabs 服务条款的内容</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 温馨提示 */}
              <div className="bg-gradient-to-r from-blue-50/90 to-indigo-50/90 rounded-2xl p-6 border border-blue-200/60 shadow-lg max-w-4xl mx-auto">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2">
                  <span className="text-2xl">💡</span>
                  温馨提示
                </h3>
                <p className="text-gray-700 leading-relaxed text-center max-w-3xl mx-auto text-lg">
                  为确保您的配音请求能够顺利处理，请在提交前检查文本内容是否符合 ElevenLabs 的<span className="font-semibold text-blue-700">内容政策</span>。如果您的内容被误判，可以尝试调整表达方式。
                </p>
              </div>

              {/* 详细说明 - 可折叠区域 */}
              <details className="group">
                <summary className="cursor-pointer list-none">
                  <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-300 group-open:rounded-b-none">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                        <span className="text-xl">📋</span>
                        审核机制详细说明
                      </h3>
                      <ChevronDown className="w-5 h-5 text-gray-500 group-open:rotate-180 transition-transform duration-300" />
                    </div>
                    <p className="text-gray-600 mt-2">点击查看详细说明</p>
                  </div>
                </summary>

                <div className="bg-white/60 backdrop-blur-sm rounded-b-2xl p-6 border border-t-0 border-gray-200/60 shadow-lg">
                  <div className="space-y-6 max-w-4xl mx-auto">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      ElevenLabs 作为全球领先的AI语音合成平台，严格遵循<span className="font-semibold text-blue-600">国际内容安全标准</span>和<span className="font-semibold text-green-600">法律法规要求</span>，对所有用户提交的文本内容实施自动化审核机制。
                    </p>

                    <p className="text-gray-700 leading-relaxed text-lg">
                      该审核系统采用先进的AI技术，能够识别和拦截可能违反服务条款的内容。这一机制旨在<span className="font-semibold text-purple-600">保护用户权益</span>、<span className="font-semibold text-orange-600">维护平台安全</span>，并确保AI语音技术的负责任使用。
                    </p>

                    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200/50">
                      <h4 className="text-lg font-bold text-gray-800 mb-3 flex items-center gap-2">
                        <span className="text-xl">⚡</span>
                        如何避免内容被拦截？
                      </h4>
                      <ul className="space-y-2 text-gray-700">
                        <li className="flex items-start gap-2">
                          <span className="text-green-600 font-bold mt-1">✓</span>
                          <span>使用积极正面的表达方式</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-600 font-bold mt-1">✓</span>
                          <span>避免使用敏感词汇和争议性话题</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-600 font-bold mt-1">✓</span>
                          <span>确保内容符合当地法律法规</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="text-green-600 font-bold mt-1">✓</span>
                          <span>使用原创内容，避免版权争议</span>
                        </li>
                      </ul>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200/50">
                      <p className="text-gray-700 leading-relaxed text-lg text-center">
                        我们理解内容审核可能会给您的创作带来一定限制，但这是为了确保平台的长期稳定运行和所有用户的安全体验。感谢您的理解与配合，让我们共同维护一个健康、安全的AI语音创作环境！
                      </p>
                    </div>
                  </div>
                </div>
              </details>

              {/* 自动关闭提示 - 已移除，改为手动关闭 */}
            </div>

            {/* 手动关闭按钮 */}
            <DialogFooter className="flex justify-center pt-4">
              <Button
                onClick={() => setShowNoticeDialog(false)}
                className="px-8 py-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 hover:from-orange-600 hover:via-red-600 hover:to-yellow-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
              >
                <CheckCircle className="w-5 h-5" />
                我知道了
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* v3功能介绍弹窗 */}
      <Dialog open={showV3IntroDialog} onOpenChange={setShowV3IntroDialog}>
        <DialogContent className="sm:max-w-2xl bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-blue-50/80" />
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200/30 to-indigo-200/30 rounded-full blur-2xl" />

            {/* 自定义关闭按钮 */}
            <button
              onClick={() => setShowV3IntroDialog(false)}
              className="absolute top-4 right-4 z-20 group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110"
            >
              <div className="relative">
                {/* 背景光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300" />
                {/* 关闭图标 */}
                <svg
                  className="relative w-5 h-5 text-gray-600 group-hover:text-purple-500 transition-colors duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
            </button>

            {/* 内容区域 */}
            <div className="relative z-10">
              <DialogHeader className="text-center space-y-4 pb-2">
                {/* 标题区域 */}
                <div className="flex items-center justify-center gap-3 mb-2">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-lg opacity-50 animate-pulse" />
                    <div className="relative p-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-full shadow-xl">
                      <Sparkles className="w-8 h-8 text-white animate-spin" style={staticStyles.sparklesAnimation} />
                    </div>
                  </div>
                </div>

                <DialogTitle className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent leading-tight text-center">
                  ✨ 释放创造力：全新 v3 模型登场！
                </DialogTitle>

                {/* 分隔线 */}
                <div className="flex items-center justify-center gap-2 py-2">
                  <div className="h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1" />
                  <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" />
                  <div className="h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1" />
                </div>
              </DialogHeader>

              <div className="space-y-6 py-4">
                {/* 引言 */}
                <div className="text-center">
                  <p className="text-lg text-gray-700 leading-relaxed font-medium">
                    全新的 v3 模型不再是冰冷地朗读文字。它能理解您植入的<span className="text-purple-600 font-semibold">"情感标签"</span>，像一位专业配音演员，用最恰当的语调和情感来演绎您的文本。
                  </p>
                </div>

                {/* 功能介绍卡片 */}
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <span className="text-2xl">🎭</span>
                    您可以尝试在文本中使用以下标签，探索各种可能性：
                  </h3>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {/* 情绪类 */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-purple-700 flex items-center gap-2">
                        <span className="text-lg">😊</span>
                        情绪类：
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {['[愉快]', '[悲伤]', '[愤怒]', '[激动]', '[担忧]', '[惊讶]'].map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm font-medium rounded-lg border border-purple-200/50 hover:scale-105 transition-transform duration-200 cursor-default"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* 风格类 */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-blue-700 flex items-center gap-2">
                        <span className="text-lg">🎨</span>
                        风格类：
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {['[旁白]', '[耳语]', '[轻声]', '[严肃]', '[广告语气]'].map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-medium rounded-lg border border-blue-200/50 hover:scale-105 transition-transform duration-200 cursor-default"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* 动作类 */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-green-700 flex items-center gap-2">
                        <span className="text-lg">🎬</span>
                        动作类：
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {['[笑声]', '[叹气]'].map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-sm font-medium rounded-lg border border-green-200/50 hover:scale-105 transition-transform duration-200 cursor-default"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 提示 */}
                  <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-xl">
                    <p className="text-center text-gray-700 font-medium flex items-center justify-center gap-2">
                      <span className="text-xl">💡</span>
                      <span>提示：您可以自由组合，创造出独一无二的声音效果！</span>
                    </p>
                  </div>
                </div>

                {/* 自动关闭提示 */}
                <div className="text-center">
                  <p className="text-sm text-gray-500 flex items-center justify-center gap-2">
                    <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    此弹窗将在 5 秒后自动关闭
                  </p>
                </div>
              </div>

              {/* 手动关闭按钮 */}
              <DialogFooter className="flex justify-center pt-4">
                <Button
                  onClick={() => setShowV3IntroDialog(false)}
                  className="px-8 py-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
                >
                  <CheckCircle className="w-5 h-5" />
                  我知道了
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>

      {/* 筛选模态框 */}
      <Dialog open={showFilterModal} onOpenChange={setShowFilterModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden p-0 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-3xl shadow-3xl">
          <div className="flex h-[80vh]">
            {/* 左侧筛选面板 */}
            <div className="w-80 bg-gradient-to-br from-purple-50/80 to-pink-50/60 border-r border-gray-200/60 p-6 overflow-y-auto">
              <DialogHeader className="mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl">
                    <Filter className="w-6 h-6 text-purple-600" />
                  </div>
                  <DialogTitle className="text-2xl font-bold text-gray-900">筛选声音</DialogTitle>
                </div>
                <DialogDescription className="text-gray-600 mt-2">
                  设置筛选条件，找到最适合的声音
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                {/* 搜索框 */}
                <div className="space-y-3">
                  <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                    <div className="p-1 bg-gradient-to-r from-blue-100 to-purple-100 rounded">
                      <Search className="w-3 h-3 text-blue-600" />
                    </div>
                    搜索声音
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10" />
                    <input
                      type="text"
                      placeholder="搜索声音名称或描述..."
                      value={tempSearchQuery}
                      onChange={(e) => setTempSearchQuery(e.target.value)}
                      className="relative w-full pl-11 pr-4 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"
                    />
                    {tempSearchQuery && (
                      <button
                        onClick={() => setTempSearchQuery("")}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* 性别筛选 */}
                <div className="space-y-3">
                  <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                    <div className="p-1 bg-gradient-to-r from-green-100 to-blue-100 rounded">
                      <Users className="w-3 h-3 text-green-600" />
                    </div>
                    性别筛选
                  </label>
                  <CustomSelect
                    value={tempSelectedGender}
                    onChange={(value) => setTempSelectedGender(value as any)}
                    options={[
                      { value: "all", label: "全部声音", icon: <Users className="w-4 h-4" /> },
                      { value: "male", label: "男生声音", icon: <MaleSelectIcon className="w-4 h-4" /> },
                      { value: "female", label: "女生声音", icon: <FemaleSelectIcon className="w-4 h-4" /> },
                      // { value: "neutral", label: "中性声音", icon: <NeutralSelectIcon className="w-4 h-4" /> }
                    ]}
                    className="border-green-400 focus:ring-green-500/10"
                    hoverColor="green"
                  />
                </div>

                {/* 【新增】收藏筛选 */}
                <div className="space-y-3">
                  <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                    <div className="p-1 bg-gradient-to-r from-pink-100 to-red-100 rounded">
                      <Heart className="w-3 h-3 text-pink-600" />
                    </div>
                    我的收藏
                  </label>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => setTempShowOnlyFavorites(!tempShowOnlyFavorites)}
                      className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-2xl border-2 transition-all duration-300 font-medium text-sm ${
                        tempShowOnlyFavorites
                          ? "border-pink-400 bg-gradient-to-r from-pink-50 to-red-50 text-pink-600 shadow-lg"
                          : "border-gray-200 bg-white hover:border-pink-300 text-gray-600 hover:text-pink-500"
                      }`}
                    >
                      <Heart className={`w-4 h-4 ${tempShowOnlyFavorites ? "fill-current" : ""}`} />
                      {tempShowOnlyFavorites ? "仅显示收藏" : "显示全部"}
                    </button>
                    <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-lg">
                      {favoriteVoiceIds.length} 个收藏
                    </div>
                  </div>
                </div>

                {/* 语言筛选 */}
                <div className="space-y-3">
                  <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                    <div className="p-1 bg-gradient-to-r from-orange-100 to-red-100 rounded">
                      <Globe className="w-3 h-3 text-orange-600" />
                    </div>
                    语言筛选
                  </label>
                  <CustomSelect
                    value={tempSelectedLanguage}
                    onChange={(value) => setTempSelectedLanguage(value as any)}
                    options={[
                      { value: "all", label: "全部语言", icon: <GlobalIcon className="w-4 h-4" /> },
                      { value: "en", label: "英语", icon: <USFlagIcon className="w-4 h-4" /> },
                      { value: "ja", label: "日语", icon: <JPFlagIcon className="w-4 h-4" /> },
                      { value: "es", label: "西班牙语", icon: <ESFlagIcon className="w-4 h-4" /> },
                      { value: "ko", label: "韩语", icon: <KRFlagIcon className="w-4 h-4" /> },
                      { value: "fr", label: "法语", icon: <FRFlagIcon className="w-4 h-4" /> }
                    ]}
                    className="border-orange-400 focus:ring-orange-500/10"
                    hoverColor="orange"
                  />
                </div>

                {/* 筛选结果统计 */}
                <div className="p-4 bg-gradient-to-r from-blue-50/80 to-purple-50/60 rounded-2xl border border-blue-200/50">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600 font-medium">
                      筛选结果
                    </div>
                    <div className="text-lg font-bold text-blue-600">
                      {tempFilteredVoices.length} 个声音
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="space-y-3 pt-4 border-t border-gray-200/60">
                  <button
                    onClick={resetFilters}
                    className="w-full px-4 py-3 text-sm text-red-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 rounded-2xl transition-all duration-200 font-medium"
                  >
                    重置筛选
                  </button>
                  <button
                    onClick={applyFilters}
                    className="w-full px-4 py-3 text-sm text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-semibold flex items-center justify-center gap-2"
                  >
                    <CheckCircle className="w-4 h-4" />
                    应用筛选 ({tempFilteredVoices.length})
                  </button>
                </div>
              </div>
            </div>

            {/* 右侧声音列表 */}
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">筛选结果</h3>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    共找到 {tempFilteredVoices.length} 个符合条件的声音
                  </p>
                  <p className="text-xs text-violet-500 bg-gray-50 px-2 py-1 rounded-lg flex items-center gap-1">
                    <Sparkles className="w-3 h-3" />
                    双击声音卡片快速选择并应用
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {tempFilteredVoices.map((voice) => (
                  <div
                    key={voice.id}
                    className={`group relative p-4 border-2 rounded-2xl transition-all duration-300 cursor-pointer hover:shadow-lg ${
                      tempSelectedVoice === voice.id
                        ? "border-blue-400 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg"
                        : "border-gray-200 bg-white hover:border-gray-300"
                    }`}
                    onClick={() => {
                      setTempSelectedVoice(voice.id)
                    }}
                    onDoubleClick={() => {
                      setTempSelectedVoice(voice.id)
                      applyFilters()
                    }}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <img
                        src={voiceIconMapping[voice.id]}
                        alt={voice.name}
                        className="w-10 h-10 rounded-full object-cover shadow-md"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 truncate">{voice.name}</h4>
                        <div className="flex items-center gap-2 text-xs">
                          <span className={`px-2 py-1 rounded-full text-white font-medium flex items-center gap-1 ${
                            voice.gender === 'male' ? 'bg-blue-500' :
                            voice.gender === 'female' ? 'bg-pink-500' : 'bg-gray-500'
                          }`}>
                            {voice.gender === 'male' ? <MaleIcon className="w-3 h-3" /> :
                             voice.gender === 'female' ? <FemaleIcon className="w-3 h-3" /> :
                             <NeutralIcon className="w-3 h-3" />}
                            {voice.gender === 'male' ? '男' : voice.gender === 'female' ? '女' : '中性'}
                          </span>
                          <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full font-medium flex items-center gap-1">
                            {voice.language === 'en' ? <USFlagIcon className="w-3 h-3" /> :
                             voice.language === 'ja' ? <JPFlagIcon className="w-3 h-3" /> :
                             voice.language === 'es' ? <ESFlagIcon className="w-3 h-3" /> :
                             voice.language === 'ko' ? <KRFlagIcon className="w-3 h-3" /> :
                             voice.language === 'fr' ? <FRFlagIcon className="w-3 h-3" /> :
                             <GlobalIcon className="w-3 h-3" />}
                            {voice.language === 'en' ? '英语' : voice.language === 'ja' ? '日语' : voice.language === 'es' ? '西班牙语' : voice.language === 'ko' ? '韩语' : voice.language === 'fr' ? '法语' : '其他'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2">{voice.description}</p>

                    {/* 【新增】收藏按钮 - 始终可见 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleVoiceFavorite(voice.id)
                      }}
                      className={`absolute top-3 right-3 p-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 ${
                        favoriteVoiceIds.includes(voice.id)
                          ? "bg-pink-100 hover:bg-pink-200"
                          : "bg-white/90 hover:bg-white"
                      }`}
                      title={favoriteVoiceIds.includes(voice.id) ? "取消收藏" : "添加收藏"}
                    >
                      <Heart className={`w-4 h-4 transition-colors duration-200 ${
                        favoriteVoiceIds.includes(voice.id)
                          ? "text-pink-600 fill-current"
                          : "text-gray-500 hover:text-pink-600"
                      }`} />
                    </button>

                    {/* 预览按钮 - 保持悬停显示，位于收藏按钮左侧 */}
                    {voice.preview && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleVoicePreview(voice.preview, voice.id)
                        }}
                        className="absolute top-3 right-14 p-2 bg-white/80 hover:bg-white rounded-full shadow-md hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                        title={previewingVoice === voice.id ? "停止预览" : "预览声音"}
                      >
                        {previewingVoice === voice.id ? (
                          <Pause className="w-4 h-4 text-blue-600" />
                        ) : (
                          <Play className="w-4 h-4 text-blue-600" />
                        )}
                      </button>
                    )}

                    {/* 选中指示器 */}
                    {tempSelectedVoice === voice.id && (
                      <div className="absolute top-3 left-3 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-md">
                        <div className="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-75" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {tempFilteredVoices.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <Search className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">没有找到匹配的声音</h3>
                  <p className="text-gray-500">请尝试调整筛选条件</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 字符限制提示 */}
      {showCharLimitToast && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
          <div className="bg-orange-500 text-white px-6 py-4 rounded-lg shadow-xl max-w-md mx-4 animate-in zoom-in-95 duration-300">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium">{charLimitMessage}</p>
              </div>
              <button
                onClick={() => setShowCharLimitToast(false)}
                className="text-white/80 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
