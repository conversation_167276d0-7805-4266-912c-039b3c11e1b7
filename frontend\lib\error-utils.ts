/**
 * 前端错误处理工具函数
 * 配合后端的结构化错误响应，提供统一的错误识别和处理
 * 包含前端敏感信息过滤功能，作为最后一道防线
 */

// 认证相关错误码
export const AUTH_ERROR_CODES = {
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',
  NO_TOKEN: 'NO_TOKEN',
  AUTH_ERROR: 'AUTH_ERROR',
  REFRESH_TOKEN_EXPIRED: 'REFRESH_TOKEN_EXPIRED'
} as const

// 错误类型
export type AuthErrorCode = typeof AUTH_ERROR_CODES[keyof typeof AUTH_ERROR_CODES]

// 扩展的错误对象接口
export interface ExtendedError extends Error {
  code?: string
}

// 前端敏感信息检测模式
const FRONTEND_SENSITIVE_PATTERNS = [
  // 文件路径和系统信息
  /[A-Za-z]:\\[\w\\.-]+/g,                    // Windows路径
  /\/[\w\/.-]+\.(js|ts|json|sql|env)/g,       // Unix路径和文件
  /node_modules/gi,                          // Node模块路径
  /at\s+[\w.]+\s+\(/g,                       // 堆栈跟踪

  // 网络和API信息
  /https?:\/\/[\w.-]+\/[\w\/.-]*/g,           // 完整URL
  /localhost:\d+/g,                          // 本地端口
  /\b(?:\d{1,3}\.){3}\d{1,3}:\d+\b/g,        // IP:端口

  // 数据库和系统错误
  /table\s+["']?\w+["']?/gi,                  // 表名
  /column\s+["']?\w+["']?/gi,                 // 列名
  /constraint\s+["']?\w+["']?/gi,             // 约束名
  /error:\s*\w+error/gi,                     // 错误类型
  /errno\s*:\s*\d+/gi,                       // 错误码

  // 敏感关键词
  /api[_-]?key/gi,                           // API密钥
  /secret/gi,                                // 密钥
  /password/gi,                              // 密码
]

// 用户友好的错误消息映射
const FRONTEND_SAFE_MESSAGES = {
  // 网络相关
  'network': '网络连接异常，请检查网络后重试',
  'timeout': '请求超时，请稍后重试',
  'fetch': '网络请求失败，请稍后重试',

  // 系统相关
  'internal': '系统暂时繁忙，请稍后再试',
  'server': '服务器暂时不可用，请稍后再试',
  'database': '数据处理异常，请稍后重试',

  // 认证相关
  'auth': '认证失败，请重新登录',
  'token': '登录会话已过期，请重新登录',
  'unauthorized': '未授权访问，请先登录',

  // 通用错误
  'unknown': '发生未知错误，请稍后重试',
  'default': '操作失败，请稍后重试'
}

/**
 * 检测错误消息中的敏感信息（前端最后防线）
 * @param message 错误消息
 * @returns 是否包含敏感信息
 */
export function containsSensitiveInfo(message: string): boolean {
  if (!message || typeof message !== 'string') {
    return false
  }

  return FRONTEND_SENSITIVE_PATTERNS.some(pattern => pattern.test(message))
}

/**
 * 清理错误消息中的敏感信息
 * @param message 原始错误消息
 * @returns 清理后的错误消息
 */
export function sanitizeErrorMessage(message: string): string {
  if (!message || typeof message !== 'string') {
    return FRONTEND_SAFE_MESSAGES.default
  }

  // 如果包含敏感信息，返回通用消息
  if (containsSensitiveInfo(message)) {
    // 根据消息内容返回相应的安全消息
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
      return FRONTEND_SAFE_MESSAGES.network
    } else if (lowerMessage.includes('timeout')) {
      return FRONTEND_SAFE_MESSAGES.timeout
    } else if (lowerMessage.includes('auth') || lowerMessage.includes('token')) {
      return FRONTEND_SAFE_MESSAGES.auth
    } else if (lowerMessage.includes('server') || lowerMessage.includes('internal')) {
      return FRONTEND_SAFE_MESSAGES.server
    } else if (lowerMessage.includes('database')) {
      return FRONTEND_SAFE_MESSAGES.database
    } else {
      return FRONTEND_SAFE_MESSAGES.unknown
    }
  }

  // 没有敏感信息，返回原始消息
  return message
}

/**
 * 判断是否为认证相关错误
 * @param error 错误对象
 * @returns 是否为认证错误
 */
export function isAuthError(error: any): boolean {
  // 优先检查错误码（最可靠）
  if (error?.code) {
    return Object.values(AUTH_ERROR_CODES).includes(error.code)
  }

  // 兼容性检查：检查错误消息
  if (error?.message) {
    const message = error.message.toLowerCase()
    return message.includes('token') ||
           message.includes('expired') ||
           message.includes('unauthorized') ||
           message.includes('401') ||
           message.includes('登录') ||
           message.includes('refresh')
  }

  return false
}

/**
 * 判断是否为token过期错误
 * @param error 错误对象
 * @returns 是否为token过期错误
 */
export function isTokenExpiredError(error: any): boolean {
  // 优先检查错误码
  if (error?.code === AUTH_ERROR_CODES.TOKEN_EXPIRED) {
    return true
  }
  
  // 兼容性检查：检查错误消息
  if (error?.message) {
    const message = error.message.toLowerCase()
    return message.includes('token expired') ||
           message.includes('expired') ||
           message.includes('过期')
  }
  
  return false
}

/**
 * 生成安全的用户友好错误消息（前端最后防线）
 * @param error 错误对象
 * @returns 安全的用户友好错误消息
 */
export function getSafeUserFriendlyMessage(error: any): string {
  let message = ''

  // 提取错误消息
  if (error?.message) {
    message = error.message
  } else if (error?.error) {
    message = error.error
  } else if (typeof error === 'string') {
    message = error
  } else {
    message = '操作失败，请稍后重试'
  }

  // 如果是认证错误，返回认证相关消息
  if (isAuthError(error)) {
    return '登录会话已过期，请重新登录'
  }

  // 清理敏感信息
  return sanitizeErrorMessage(message)
}

/**
 * 获取用户友好的错误消息
 * @param error 错误对象
 * @returns 用户友好的错误消息
 */
export function getAuthErrorMessage(error: any): {
  title: string
  description: string
  shouldRedirect: boolean
} {
  if (isAuthError(error)) {
    // 【修复】检查错误对象上的shouldRedirect属性，默认为true
    const shouldRedirect = error?.shouldRedirect !== undefined ? error.shouldRedirect : true

    return {
      title: "认证失败",
      description: "会话已过期，正在跳转到登录页面...",
      shouldRedirect: shouldRedirect
    }
  }

  // 业务错误或网络错误 - 使用安全消息
  return {
    title: "操作失败",
    description: getSafeUserFriendlyMessage(error),
    shouldRedirect: false
  }
}

/**
 * 统一的认证错误处理函数
 * @param error 错误对象
 * @param onAuthError 认证错误回调（可选）
 * @returns 处理结果
 */
export function handleAuthError(
  error: any,
  onAuthError?: () => void
): {
  isAuthError: boolean
  message: string
  shouldRedirect: boolean
} {
  const isAuth = isAuthError(error)
  
  if (isAuth && onAuthError) {
    onAuthError()
  }
  
  const errorInfo = getAuthErrorMessage(error)
  
  return {
    isAuthError: isAuth,
    message: errorInfo.description,
    shouldRedirect: errorInfo.shouldRedirect
  }
}

/**
 * 为页面组件提供的错误处理hook辅助函数
 * @param error 错误对象
 * @returns UI状态更新信息
 */
export function getErrorUIState(error: any): {
  statusDisplay: string
  toastTitle: string
  toastDescription: string
  variant: 'default' | 'destructive'
} {
  if (isAuthError(error)) {
    return {
      statusDisplay: "请重新登录",
      toastTitle: "认证失败",
      toastDescription: "会话已过期，正在跳转到登录页面...",
      variant: 'destructive'
    }
  }
  
  // 检查是否为业务错误（如卡密无效等）
  if (error?.message?.includes('卡密')) {
    return {
      statusDisplay: "充值失败",
      toastTitle: "充值失败", 
      toastDescription: "卡密无效或已使用，请检查后重试",
      variant: 'destructive'
    }
  }
  
  // 网络或其他错误 - 使用安全消息
  return {
    statusDisplay: "获取失败",
    toastTitle: "操作失败",
    toastDescription: getSafeUserFriendlyMessage(error),
    variant: 'destructive'
  }
}

/**
 * 生成用户友好的错误消息（向后兼容版本）
 * @param error 错误对象
 * @returns 用户友好的错误消息
 */
export function getUserFriendlyMessage(error: any): string {
  // 使用安全版本
  return getSafeUserFriendlyMessage(error)
}
