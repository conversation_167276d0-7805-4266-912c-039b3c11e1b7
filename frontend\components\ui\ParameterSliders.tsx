"use client"

import React, { useMemo } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Settings } from "lucide-react";

interface ParameterSlidersProps {
  stability: number[];
  similarity: number[];
  style: number[];
  speechRate: number[];
  selectedModel: string;
  onStabilityChange: (value: number[]) => void;
  onSimilarityChange: (value: number[]) => void;
  onStyleChange: (value: number[]) => void;
  onSpeechRateChange: (value: number[]) => void;
}

// 使用 React.memo 包裹参数滑块组件 - 防止父组件重渲染时重新渲染
export const ParameterSliders = React.memo(function ParameterSliders({
  stability,
  similarity,
  style,
  speechRate,
  selectedModel,
  onStabilityChange,
  onSimilarityChange,
  onStyleChange,
  onSpeechRateChange
}: ParameterSlidersProps) {
  console.log("ParameterSliders is rendering"); // 性能优化后，这个只会在参数变化时打印

  // 🔧 性能优化：使用useMemo包裹参数配置数组，避免每次重渲染都创建新数组
  const parameters = useMemo(() => [
    {
      label: "稳定性",
      value: stability,
      setter: onStabilityChange,
      color: "blue",
      max: 1,
      min: 0,
      step: 0.01,
    },
    {
      label: "相似度",
      value: similarity,
      setter: onSimilarityChange,
      color: "purple",
      max: 1,
      min: 0,
      step: 0.01,
    },
    {
      label: "风格",
      value: style,
      setter: onStyleChange,
      color: "green",
      max: 1,
      min: 0,
      step: 0.01,
    },
    {
      label: "语速",
      value: speechRate,
      setter: onSpeechRateChange,
      color: "orange",
      max: 1.2,
      min: 0.7,
      step: 0.01,
    },
  ], [stability, similarity, style, speechRate, onStabilityChange, onSimilarityChange, onStyleChange, onSpeechRateChange]);

  // 根据模型过滤参数
  const filteredParameters = parameters.filter(param => {
    // 当选择 Eleven v3 模型时，只保留"稳定性"参数
    if (selectedModel === "eleven_v3") {
      return param.label === "稳定性";
    }
    // 当选择 Eleven Turbo v2 或 Eleven Turbo v2.5 模型时，隐藏"风格"参数
    if ((selectedModel === "eleven_turbo_v2" || selectedModel === "eleven_turbo_v2_5") && param.label === "风格") {
      return false;
    }
    return true;
  });

  return (
    <Card className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 rounded-2xl overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <CardContent className="p-6 relative">
        <div className="flex items-center gap-3 mb-6">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md opacity-50" />
            <div className="relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
              <Settings className="w-5 h-5 text-white" />
            </div>
          </div>
          <h3 className="text-gradient-optimized text-lg font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
            参数调节
          </h3>
        </div>

        <div className="space-y-5">
          {filteredParameters.map((param, index) => (
            <div key={param.label} className="group/param" style={{ animationDelay: `${index * 100}ms` }}>
              <div className="flex justify-between items-center mb-3">
                <label className="text-sm font-semibold text-gray-700 group-hover/param:text-gray-900 transition-colors duration-300">
                  {param.label}
                </label>
                <span
                  className={`text-sm font-mono px-2.5 py-1.5 rounded-lg font-bold transition-all duration-300 group-hover/param:scale-110 ${
                    param.color === "blue"
                      ? "bg-blue-100 text-blue-800 group-hover/param:bg-blue-200"
                      : param.color === "purple"
                        ? "bg-purple-100 text-purple-800 group-hover/param:bg-purple-200"
                        : param.color === "green"
                          ? "bg-green-100 text-green-800 group-hover/param:bg-green-200"
                          : "bg-orange-100 text-orange-800 group-hover/param:bg-orange-200"
                  }`}
                >
                  {param.value[0].toFixed(2)}
                </span>
              </div>
              <div className="relative">
                <Slider
                  value={param.value}
                  onValueChange={(value) => {
                    // 当选择 eleven_v3 模型且参数是稳定性时，限制为离散值 0/0.5/1
                    if (selectedModel === "eleven_v3" && param.label === "稳定性") {
                      const newValue = value[0];
                      let discreteValue;
                      if (newValue <= 0.25) {
                        discreteValue = 0;
                      } else if (newValue <= 0.75) {
                        discreteValue = 0.5;
                      } else {
                        discreteValue = 1;
                      }
                      param.setter([discreteValue]);
                    } else {
                      param.setter(value);
                    }
                  }}
                  max={param.max}
                  min={param.min}
                  step={selectedModel === "eleven_v3" && param.label === "稳定性" ? 0.5 : param.step}
                  className="w-full group-hover/param:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover/param:opacity-100 transition-opacity duration-500 pointer-events-none animate-shimmer" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});
