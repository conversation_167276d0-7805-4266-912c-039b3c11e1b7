"use client"

import React, { useMemo, useCallback } from 'react';
import { Button } from "@/components/ui/button";

// 使用 React.memo 包裹充值按钮组件 - 防止父组件重渲染时重新渲染
export const RechargeButton = React.memo(function RechargeButton() {
  console.log("RechargeButton is rendering"); // 性能优化后，这个只会在必要时打印

  // 🔧 性能优化：使用useCallback稳定函数引用
  const handleRechargeClick = useCallback(() => {
    window.location.href = "/recharge";
  }, []);

  // 🔧 性能优化：使用useMemo缓存静态样式对象
  const staticStyles = useMemo(() => ({
    breatheAnimation: { animation: 'breathe 3s ease-in-out infinite' },
    breatheGlowAnimation: { animation: 'breathe-glow 4s ease-in-out infinite' },
    breatheIconAnimation: { animation: 'breathe 2.5s ease-in-out infinite' },
    breatheSparkleAnimation: { animation: 'breathe 2s ease-in-out infinite' },
    breatheRippleAnimation: { animation: 'breathe 3.5s ease-in-out infinite' }
  }), []);

  // 🔧 性能优化：使用useMemo缓存粒子数组
  const particles = useMemo(() => [...Array(3)], []);

  return (
    <Button
      onClick={handleRechargeClick}
      className="group relative bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white px-3 py-2 lg:px-5 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 flex items-center gap-1 lg:gap-2 text-sm lg:text-base font-semibold overflow-hidden border border-white/20 backdrop-blur-sm mr-1 lg:mr-4"
    >
      {/* Animated background overlay with breathe effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" style={staticStyles.breatheAnimation} />

      {/* Glowing border effect with breathe */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 opacity-0 group-hover:opacity-30 blur-sm transition-all duration-500 -z-10" style={staticStyles.breatheGlowAnimation} />

      {/* Enhanced icon container */}
      <div className="relative w-4 h-4 lg:w-6 lg:h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 group-hover:rotate-12 group-hover:scale-110">
        <div className="absolute inset-0 bg-gradient-to-r from-yellow-300 to-orange-300 rounded-full opacity-60 group-hover:opacity-80 transition-opacity duration-300" style={staticStyles.breatheIconAnimation} />
        <span className="relative text-xs lg:text-sm font-bold text-white drop-shadow-sm">¥</span>

        {/* Sparkle effect with breathe */}
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={staticStyles.breatheSparkleAnimation} />
      </div>

      {/* Text content with enhanced styling */}
      <div className="relative flex flex-col items-start">
        <span className="hidden sm:inline text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300">
          充值中心
        </span>
        <span className="sm:hidden text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300">
          充值
        </span>

        {/* Subtle underline animation */}
        <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-300 to-orange-300 group-hover:w-full transition-all duration-500 rounded-full" />
      </div>

      {/* Floating particles effect with breathe */}
      <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        {particles.map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-yellow-300 rounded-full"
            style={{
              left: `${20 + i * 25}%`,
              top: `${30 + i * 15}%`,
              animation: `breathe ${1.5 + i * 0.3}s ease-in-out infinite`,
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </div>

      {/* Ripple effect on hover with breathe */}
      <div className="absolute inset-0 rounded-2xl bg-white/10 scale-0 group-hover:scale-100 opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out" style={staticStyles.breatheRippleAnimation} />

      {/* 动画优化样式 */}
      <style jsx>{`
        div[style*="animation: 'breathe"] {
          will-change: opacity, transform;
          contain: layout style paint;
          transform: translateZ(0);
        }
        div[style*="animation: 'breathe-glow"] {
          will-change: opacity, box-shadow;
          contain: layout style paint;
        }
      `}</style>
    </Button>
  );
});
